// import type { Institution, InstitutionListParams, InstitutionListResponse } from './types/institution'
// import { http } from '@/http/http'

// /**
//  * 获取机构列表
//  */
// export function getInstitutionList(params?: InstitutionListParams) {
//   return http.Get<InstitutionListResponse>('/institution/list', {
//     params: {
//       type: params?.type,
//       keyword: params?.keyword,
//       page: params?.page || 1,
//       pageSize: params?.pageSize || 10,
//     },
//   })
// }

// /**
//  * 获取机构详情
//  */
// export function getInstitutionDetail(id: number) {
//   return http.Get<Institution>(`/institution/detail/${id}`)
// }

// /**
//  * 获取机构分类
//  */
// export function getInstitutionTypes() {
//   return http.Get<string[]>('/institution/types')
// }

// /**
//  * 搜索机构
//  */
// export function searchInstitutions(keyword: string) {
//   return http.Get<InstitutionListResponse>('/institution/search', {
//     params: { keyword },
//   })
// }
