<route lang="jsonc" type="page">
{
  "style": {
    "navigationBarTitleText": "产品详情"
  }
}
</route>

<script setup lang="ts">
import { onLoad } from '@dcloudio/uni-app'
import { ref } from 'vue'

// 产品详情数据接口
interface ProductDetail {
  id: number
  title: string
  company: string
  image: string
  description: string
  category: string
  technology: string
  application: string
  contact: string
  createTime: string
}

const productDetail = ref<ProductDetail>({
  id: 0,
  title: '',
  company: '',
  image: '',
  description: '',
  category: '',
  technology: '',
  application: '',
  contact: '',
  createTime: '',
})

// 模拟产品数据
const productData: Record<number, ProductDetail> = {
  1: {
    id: 1,
    title: '智能家居控制系统',
    company: '科技股份有限公司',
    image: 'https://picsum.photos/400/300?random=10',
    description: '基于物联网技术的智能家居控制系统，实现家庭设备的智能化管理和远程控制。系统支持多种通信协议，具有高可靠性和安全性。',
    category: '智能家居',
    technology: '物联网、人工智能、云计算',
    application: '家庭自动化、智能安防、能源管理',
    contact: '张经理 - 13800138000',
    createTime: '2024-01-15',
  },
  2: {
    id: 2,
    title: '工业机器人手臂',
    company: '智能制造有限公司',
    image: 'https://picsum.photos/400/300?random=11',
    description: '高精度六轴工业机器人手臂，采用先进的伺服控制系统和机器视觉技术，适用于精密装配、焊接、喷涂等工业场景。',
    category: '工业自动化',
    technology: '机器人技术、机器视觉、伺服控制',
    application: '汽车制造、电子装配、食品加工',
    contact: '李工程师 - 13900139000',
    createTime: '2024-02-20',
  },
  3: {
    id: 3,
    title: '医疗影像分析软件',
    company: '医疗科技公司',
    image: 'https://picsum.photos/400/300?random=12',
    description: '基于深度学习的医疗影像分析软件，能够自动识别和分析CT、MRI等医学影像，辅助医生进行疾病诊断和治疗方案制定。',
    category: '医疗健康',
    technology: '深度学习、计算机视觉、医学影像处理',
    application: '医院诊断、医学研究、健康筛查',
    contact: '王博士 - 13700137000',
    createTime: '2024-03-10',
  },
}

onLoad((options) => {
  const id = Number(options?.id) || 1
  productDetail.value = productData[id] || productData[1]
})

// 返回上一页
function goBack() {
  uni.navigateBack()
}

// 联系企业
function contactCompany() {
  uni.makePhoneCall({
    phoneNumber: productDetail.value.contact.split('-')[1].trim(),
  })
}
</script>

<template>
  <view class="min-h-screen bg-white pb-20">
    <!-- 返回按钮 -->
    <view class="px-4 pt-4">
      <wd-icon name="arrow-left" size="20" @click="goBack" />
    </view>

    <!-- 产品图片 -->
    <view class="product-image mt-4">
      <image
        :src="productDetail.image"
        class="h-60 w-full"
        mode="aspectFill"
      />
    </view>

    <!-- 产品基本信息 -->
    <view class="px-4 pt-4">
      <text class="block text-xl font-bold">
        {{ productDetail.title }}
      </text>
      <text class="mt-1 block text-sm text-gray-600">
        {{ productDetail.company }}
      </text>
      <text class="mt-3 block text-base text-gray-800">
        {{ productDetail.description }}
      </text>
    </view>

    <!-- 详细信息卡片 -->
    <view class="px-4 pt-6">
      <view class="rounded-lg bg-gray-50 p-4 space-y-3">
        <view class="flex items-center">
          <text class="w-20 text-gray-500">
            技术领域：
          </text>
          <text class="flex-1">
            {{ productDetail.technology }}
          </text>
        </view>
        <view class="flex items-center">
          <text class="w-20 text-gray-500">
            应用场景：
          </text>
          <text class="flex-1">
            {{ productDetail.application }}
          </text>
        </view>
        <view class="flex items-center">
          <text class="w-20 text-gray-500">
            产品分类：
          </text>
          <text class="flex-1">
            {{ productDetail.category }}
          </text>
        </view>
        <view class="flex items-center">
          <text class="w-20 text-gray-500">
            发布时间：
          </text>
          <text class="flex-1">
            {{ productDetail.createTime }}
          </text>
        </view>
      </view>
    </view>

    <!-- 联系方式 -->
    <view class="px-4 pt-6">
      <view class="rounded-lg bg-blue-50 p-4">
        <text class="mb-2 block text-blue-600 font-medium">
          联系方式
        </text>
        <text class="text-blue-800">
          {{ productDetail.contact }}
        </text>
        <view class="mt-3">
          <wd-button type="primary" size="small" @click="contactCompany">
            立即联系
          </wd-button>
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.product-image {
  border-bottom: 1px solid #f3f4f6;
}
</style>
