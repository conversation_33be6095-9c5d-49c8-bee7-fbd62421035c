<script setup lang="ts">
defineOptions({
  name: 'EmptyState',
})

defineProps<{
  text: string
  subtext?: string
  icon?: string
  padding?: string
}>()
</script>

<template>
  <view class="empty-state" :style="{ padding: padding || '120rpx 0' }">
    <image
      class="empty-icon"
      :src="icon || '/static/images/home/<USER>'"
      mode="aspectFit"
    />
    <text class="empty-text">
      {{ text }}
    </text>
    <text v-if="subtext" class="empty-subtext">
      {{ subtext }}
    </text>
  </view>
</template>

<style lang="scss" scoped>
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 32rpx;
  opacity: 0.6;
}

.empty-text {
  font-size: 32rpx;
  color: #969ba4;
  line-height: 44rpx;
  font-weight: 500;
  margin-bottom: 16rpx;
}

.empty-subtext {
  font-size: 28rpx;
  color: #c8c9cc;
  line-height: 40rpx;
}
</style>
