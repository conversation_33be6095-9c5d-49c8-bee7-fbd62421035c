import type { TabBar } from '@uni-helper/vite-plugin-uni-pages'

/**
 * tabbar 选择的策略，更详细的介绍见 tabbar.md 文件
 * 0: 'NO_TABBAR' `无 tabbar`
 * 1: 'NATIVE_TABBAR'  `完全原生 tabbar`
 * 2: 'CUSTOM_TABBAR_WITH_CACHE' `有缓存自定义 tabbar`
 * 3: 'CUSTOM_TABBAR_WITHOUT_CACHE' `无缓存自定义 tabbar`
 *
 * 温馨提示：本文件的任何代码更改了之后，都需要重新运行，否则 pages.json 不会更新导致错误
 */
export const TABBAR_MAP = {
  NO_TABBAR: 0,
  NATIVE_TABBAR: 1,
  CUSTOM_TABBAR_WITH_CACHE: 2,
  CUSTOM_TABBAR_WITHOUT_CACHE: 3,
}
// TODO：通过这里切换使用tabbar的策略
export const selectedTabbarStrategy = TABBAR_MAP.NATIVE_TABBAR

// selectedTabbarStrategy==NATIVE_TABBAR(1) 时，需要填 iconPath 和 selectedIconPath
// selectedTabbarStrategy==CUSTOM_TABBAR(2,3) 时，需要填 icon 和 iconType
// selectedTabbarStrategy==NO_TABBAR(0) 时，tabbarList 不生效
export const tabbarList: TabBar['list'] = [
  {
    iconPath: 'static/tabbar/home.png',
    selectedIconPath: 'static/tabbar/homeHL.png',
    pagePath: 'pages/index/index',
    text: '首页',
    // icon: 'i-carbon-home',
    // 选用 UI 框架自带的 icon 时，iconType 为 uiLib
    // iconType: 'unocss',
  },
  {
    iconPath: 'static/tabbar/industryChain.png',
    selectedIconPath: 'static/tabbar/industryChainHL.png',
    pagePath: 'pages/industry-chain/index',
    text: '产业链',
    // icon: 'i-carbon-chart-line',
    // iconType: 'unocss',
  },
  {
    iconPath: 'static/tabbar/discovery.png',
    selectedIconPath: 'static/tabbar/discoveryHL.png',
    pagePath: 'pages/discovery/index',
    text: '发现',
    // icon: 'i-carbon-search',
    // iconType: 'unocss',
  },
  {
    iconPath: 'static/tabbar/institution.png',
    selectedIconPath: 'static/tabbar/institutionHL.png',
    pagePath: 'pages/institution/index',
    text: '机构',
    // icon: 'i-carbon-building',
    // iconType: 'unocss',
  },
  {
    iconPath: 'static/tabbar/profile.png',
    selectedIconPath: 'static/tabbar/profileHL.png',
    pagePath: 'pages/profile/index',
    text: '我的',
    // icon: 'i-carbon-user-avatar',
    // iconType: 'unocss',
  },
]

// NATIVE_TABBAR(1) 和 CUSTOM_TABBAR_WITH_CACHE(2) 时，需要tabbar缓存
export const cacheTabbarEnable = selectedTabbarStrategy === TABBAR_MAP.NATIVE_TABBAR
  || selectedTabbarStrategy === TABBAR_MAP.CUSTOM_TABBAR_WITH_CACHE

const _tabbar: TabBar = {
  color: '#999999',
  selectedColor: '#0365F0',
  backgroundColor: '#FFFFFF',
  borderStyle: 'black',
  height: '50px',
  fontSize: '12px',
  iconWidth: '24px',
  spacing: '3px',
  list: tabbarList,
}

// 0和1 需要显示底部的tabbar的各种配置，以利用缓存
export const tabBar = cacheTabbarEnable ? _tabbar : undefined
