<route lang="jsonc" type="page">
{
  "layout": "default",
  "style": {
    "navigationBarTitleText": "分包页面"
  }
}
</route>

<script lang="ts" setup>
// code here
</script>

<template>
  <view class="text-center">
    <view class="m-8">
      http://localhost:9000/#/pages-sub/demo/index
    </view>
    <view class="text-green-500">
      分包页面demo
    </view>
  </view>
</template>

<style lang="scss" scoped>
//
</style>
