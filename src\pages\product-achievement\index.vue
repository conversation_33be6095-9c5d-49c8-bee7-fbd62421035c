<route lang="jsonc" type="page">
{
  "style": {
    "navigationBarTitleText": "产品成果"
  }
}
</route>

<script setup lang="ts">
// 产品成果列表页面
defineOptions({
  name: 'ProductAchievement',
})

// 产品成果数据（与发现页面保持一致）
const productList = [
  {
    id: 1,
    image: 'https://picsum.photos/100/100?random=4',
    title: '智能家居控制系统',
    company: '科技股份有限公司',
    desc: '基于物联网的智能家居解决方案',
  },
  {
    id: 2,
    image: 'https://picsum.photos/100/100?random=5',
    title: '工业机器人手臂',
    company: '智能制造有限公司',
    desc: '高精度工业自动化设备',
  },
  {
    id: 3,
    image: 'https://picsum.photos/100/100?random=6',
    title: '医疗影像分析软件',
    company: '医疗科技公司',
    desc: 'AI辅助医疗诊断系统',
  },
  // 可以添加更多产品数据
  {
    id: 4,
    image: 'https://picsum.photos/100/100?random=7',
    title: '智能农业监测系统',
    company: '农业科技公司',
    desc: '基于物联网的农业环境监测解决方案',
  },
  {
    id: 5,
    image: 'https://picsum.photos/100/100?random=8',
    title: '新能源充电桩',
    company: '能源科技有限公司',
    desc: '智能电动汽车充电基础设施',
  },
  {
    id: 6,
    image: 'https://picsum.photos/100/100?random=9',
    title: '智能安防监控系统',
    company: '安防技术有限公司',
    desc: 'AI人脸识别智能安防解决方案',
  },
]

// 跳转到产品详情页面
function goToProductDetail(id: number) {
  uni.navigateTo({
    url: `/pages/tech-achievement/detail?id=${id}`,
  })
}

// 返回上一页
function goBack() {
  uni.navigateBack()
}
</script>

<template>
  <view class="min-h-screen bg-white pb-20">
    <!-- 返回按钮 -->
    <view class="px-4 pt-4">
      <wd-icon name="arrow-left" size="20" @click="goBack" />
    </view>

    <!-- 页面标题 -->
    <view class="px-4 pt-2">
      <text class="text-xl font-bold">
        产品成果列表
      </text>
    </view>

    <!-- 产品成果列表 -->
    <view class="px-4 pt-4">
      <view class="product-list grid grid-cols-2 gap-3">
        <view
          v-for="product in productList"
          :key="product.id"
          class="product-item flex flex-col rounded-lg bg-gray-50 p-3"
          @click="goToProductDetail(product.id)"
        >
          <image
            :src="product.image"
            class="mb-2 h-24 w-full rounded-md"
            mode="aspectFill"
          />
          <view class="flex-1">
            <text class="line-clamp-2 mb-1 block text-sm font-medium">
              {{ product.title }}
            </text>
            <text class="block text-xs text-gray-600">
              {{ product.company }}
            </text>
            <text class="line-clamp-2 mt-1 block text-xs text-gray-500">
              {{ product.desc }}
            </text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.product-item {
  transition: all 0.2s ease;

  &:active {
    background-color: #e5e7eb;
    transform: scale(0.98);
  }
}

// 两列布局优化样式
.product-list {
  .product-item {
    min-height: 200px;

    image {
      object-fit: cover;
    }
  }
}

// 文本溢出处理
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
