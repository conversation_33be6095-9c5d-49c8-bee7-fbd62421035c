<route lang="jsonc" type="page">
{
  "style": {
    "navigationBarTitleText": "企业列表"
  }
}
</route>

<script setup lang="ts">
import { onMounted, ref } from 'vue'

interface Company {
  id: string
  name: string
  level: string
  category: string
  item: string
  description: string
  logo: string
  contact: string
}

defineOptions({
  name: 'CompanyList',
})

// 静态企业数据
const companies: Company[] = [
  {
    id: '1',
    name: '华为技术有限公司',
    level: '上游',
    category: '硬件设备',
    item: '芯片',
    description: '全球领先的信息与通信技术解决方案供应商',
    logo: '/static/images/default-avatar.png',
    contact: '400-830-8300',
  },
  {
    id: '2',
    name: '中芯国际',
    level: '上游',
    category: '硬件设备',
    item: '芯片',
    description: '中国内地规模最大、技术最先进的集成电路芯片制造企业',
    logo: '/static/images/default-avatar.png',
    contact: '021-6101-8888',
  },
  {
    id: '3',
    name: '海康威视',
    level: '上游',
    category: '硬件设备',
    item: '传感器',
    description: '全球领先的安防产品及行业解决方案提供商',
    logo: '/static/images/default-avatar.png',
    contact: '400-700-5998',
  },
  {
    id: '4',
    name: '大华股份',
    level: '上游',
    category: '硬件设备',
    item: '传感器',
    description: '全球领先的智慧物联解决方案提供商和运营服务商',
    logo: '/static/images/default-avatar.png',
    contact: '400-672-8166',
  },
  {
    id: '5',
    name: '阿里云',
    level: '上游',
    category: '数据服务',
    item: '云计算服务',
    description: '全球领先的云计算及人工智能科技公司',
    logo: '/static/images/default-avatar.png',
    contact: '95187',
  },
  {
    id: '6',
    name: '腾讯云',
    level: '上游',
    category: '数据服务',
    item: '云计算服务',
    description: '腾讯集团倾力打造的云计算品牌',
    logo: '/static/images/default-avatar.png',
    contact: '4009-100-100',
  },
  {
    id: '7',
    name: '百度智能云',
    level: '上游',
    category: '数据服务',
    item: '大数据',
    description: '百度推出的智能云计算服务',
    logo: '/static/images/default-avatar.png',
    contact: '400-890-0088',
  },
  {
    id: '8',
    name: '华为云',
    level: '上游',
    category: '数据服务',
    item: '大数据',
    description: '华为公司推出的云计算服务',
    logo: '/static/images/default-avatar.png',
    contact: '950808',
  },
]

const filteredCompanies = ref<Company[]>([])
const currentLevel = ref('')
const currentCategory = ref('')
const currentItem = ref('')

// 从路由参数获取筛选条件
onMounted(() => {
  // 使用更简单的方式获取参数
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1] as any
  const options = currentPage?.options || {}

  currentLevel.value = decodeURIComponent(options.level || '')
  currentCategory.value = decodeURIComponent(options.category || '')
  currentItem.value = decodeURIComponent(options.item || '')

  filterCompanies()
})

// 筛选企业
function filterCompanies() {
  filteredCompanies.value = companies.filter((company) => {
    let match = true
    if (currentLevel.value)
      match = match && company.level === currentLevel.value
    if (currentCategory.value)
      match = match && company.category === currentCategory.value
    if (currentItem.value)
      match = match && company.item === currentItem.value
    return match
  })
}

// 跳转到企业详情
function goToCompanyDetail(companyId: string) {
  uni.navigateTo({
    url: `/pages/industry-chain/company-detail?id=${companyId}`,
  })
}
</script>

<template>
  <view class="bg-white px-4 pt-2">
    <!-- 筛选信息 -->
    <view class="filter-info mb-4 rounded-lg bg-gray-50 p-3">
      <text class="text-sm text-gray-600">
        当前筛选:
        <text class="text-primary">
          {{ currentLevel }}
        </text>
        <text v-if="currentCategory">
          > {{ currentCategory }}
        </text>
        <text v-if="currentItem">
          > {{ currentItem }}
        </text>
      </text>
    </view>

    <!-- 企业列表 -->
    <view class="company-list">
      <wd-cell
        v-for="company in filteredCompanies"
        :key="company.id"
        :title="company.name"
        :label="company.description"
        is-link
        center
        @click="goToCompanyDetail(company.id)"
      >
        <template #icon>
          <image :src="company.logo" class="mr-3 h-10 w-10 rounded" />
        </template>
      </wd-cell>

      <view v-if="filteredCompanies.length === 0" class="py-10 text-center text-gray-400">
        暂无相关企业
      </view>
    </view>
  </view>
</template>

<style scoped>
.text-primary {
  color: #018d71;
}
</style>
