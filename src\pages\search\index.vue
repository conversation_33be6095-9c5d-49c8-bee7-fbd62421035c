<route lang="jsonc" type="page">
{
  "style": {
    "navigationBarTitleText": "搜索结果",
    "navigationStyle": "custom"
  }
}
</route>

<script setup lang="ts">
// 搜索结果页面
defineOptions({
  name: 'SearchResult',
})

// 模拟搜索结果数据
const searchResults = ref<any[]>([])
const isLoading = ref(false)

// 模拟搜索函数 - 切换有/无结果状态
function toggleSearchResults() {
  isLoading.value = true

  setTimeout(() => {
    if (searchResults.value.length === 0) {
      // 添加模拟搜索结果
      searchResults.value = [
        {
          id: 1,
          title: '星际荣耀双曲线一号遥十运载火箭发射成功',
          date: '2021-09-01',
          imageSrc: 'https://picsum.photos/80',
        },
        {
          id: 2,
          title: '中国航天科技集团发布新一代运载火箭计划',
          date: '2021-09-15',
          imageSrc: 'https://picsum.photos/80',
        },
        {
          id: 3,
          title: '民营航天企业突破关键技术，实现商业化运营',
          date: '2021-10-01',
          imageSrc: 'https://picsum.photos/80',
        },
      ]
    }
    else {
      // 清空结果，显示无结果状态
      searchResults.value = []
    }
    isLoading.value = false
  }, 500)
}

// 初始化时显示无结果状态
toggleSearchResults()
</script>

<template>
  <view class="search-page">
    <navabar title="搜索结果" />

    <!-- 测试按钮（演示用，后续可移除） -->
    <!-- <view class="test-toggle-btn">
      <wd-button type="primary" size="small" @click="toggleSearchResults">
        {{ searchResults.length ? '显示无结果' : '显示有结果' }}
      </wd-button>
    </view> -->

    <!-- 加载状态 -->
    <view v-if="isLoading" class="loading-state">
      <wd-skeleton :row="3" />
    </view>

    <!-- 有搜索结果 -->
    <view v-else-if="searchResults.length" class="results-container">
      <view v-for="item in searchResults" :key="item.id" class="pt-24rpx">
        <real-time-info-card
          :title="item.title"
          :date="item.date"
          :image-src="item.imageSrc"
        />
      </view>
    </view>

    <!-- 无搜索结果 -->
    <EmptyState
      v-else-if="!searchResults.length"
      text="暂无搜索结果"
      subtext="换个关键词试试吧"
    />
  </view>
</template>

<style lang="scss" scoped>
.search-page {
  // background: #F4F4F4;
  padding: 0 32rpx;
  min-height: 100vh;
  box-sizing: border-box;
}

// 测试按钮样式（演示用）
.test-toggle-btn {
  padding: 24rpx 0;
  text-align: center;
}

// 加载状态
.loading-state {
  padding: 80rpx 0;
}

// 空状态样式
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 0;
  text-align: center;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 32rpx;
  opacity: 0.6;
}

.empty-text {
  font-size: 32rpx;
  color: #969ba4;
  line-height: 44rpx;
  font-weight: 500;
  margin-bottom: 16rpx;
}

.empty-subtext {
  font-size: 28rpx;
  color: #c8c9cc;
  line-height: 40rpx;
}
</style>
