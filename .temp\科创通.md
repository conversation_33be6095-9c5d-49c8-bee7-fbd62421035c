---
title: 科创通
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# 科创通

Base URLs:

# Authentication

# 业务模块/第三方机构

<a id="opIdedit"></a>

## PUT 修改第三方机构

PUT /biz/thirdPartyOrg

修改第三方机构

> Body 请求参数

```json
{
  "id": 0,
  "name": "string",
  "logo": 0,
  "category": "string",
  "content": "string",
  "serviceScope": "string",
  "attachment": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|[BizThirdPartyOrgBo](#schemabizthirdpartyorgbo)| 否 |none|

> 返回示例

> 200 Response

```
{"code":0,"msg":"string","data":null}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[RVoid](#schemarvoid)|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|string|

<a id="opIdadd"></a>

## POST 新增第三方机构

POST /biz/thirdPartyOrg

新增第三方机构

> Body 请求参数

```json
{
  "id": 0,
  "name": "string",
  "logo": 0,
  "category": "string",
  "content": "string",
  "serviceScope": "string",
  "attachment": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|[BizThirdPartyOrgBo](#schemabizthirdpartyorgbo)| 否 |none|

> 返回示例

> 200 Response

```
{"code":0,"msg":"string","data":null}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[RVoid](#schemarvoid)|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|string|

<a id="opIdexport"></a>

## POST 导出第三方机构列表

POST /biz/thirdPartyOrg/export

导出第三方机构列表

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|query|integer(int64)| 是 |id|
|name|query|string| 是 |名称|
|logo|query|integer(int64)| 否 |logo|
|category|query|string| 是 |分类|
|content|query|string| 否 |内容|
|serviceScope|query|string| 否 |服务范围|
|attachment|query|string| 否 |附件|

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|None|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|None|

<a id="opIdgetInfo"></a>

## GET 获取第三方机构详细信息

GET /biz/thirdPartyOrg/{id}

获取第三方机构详细信息

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|path|integer(int64)| 是 |主键|

> 返回示例

> 200 Response

```
{"code":0,"msg":"string","data":{"id":0,"name":"string","logo":0,"logoUrl":"string","category":"string","content":"string","serviceScope":"string","attachment":"string","attachmentUrl":"string","createTime":"2019-08-24T14:15:22Z","createBy":"string"}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[RBizThirdPartyOrgVo](#schemarbizthirdpartyorgvo)|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|string|

<a id="opIdlist"></a>

## GET 查询第三方机构列表

GET /biz/thirdPartyOrg/list

查询第三方机构列表

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|query|integer(int64)| 是 |id|
|name|query|string| 是 |名称|
|logo|query|integer(int64)| 否 |logo|
|category|query|string| 是 |分类|
|content|query|string| 否 |内容|
|serviceScope|query|string| 否 |服务范围|
|attachment|query|string| 否 |附件|
|pageSize|query|integer(int32)| 否 |分页大小|
|pageNum|query|integer(int32)| 否 |当前页数|
|orderByColumn|query|string| 否 |排序列|
|isAsc|query|string| 否 |排序的方向desc或者asc|

> 返回示例

> 200 Response

```
{"total":0,"rows":[{"id":0,"name":"string","logo":0,"logoUrl":"string","category":"string","content":"string","serviceScope":"string","attachment":"string","attachmentUrl":"string","createTime":"2019-08-24T14:15:22Z","createBy":"string"}],"code":0,"msg":"string"}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[TableDataInfoBizThirdPartyOrgVo](#schematabledatainfobizthirdpartyorgvo)|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|string|

<a id="opIdremove"></a>

## DELETE 删除第三方机构

DELETE /biz/thirdPartyOrg/{ids}

删除第三方机构

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|ids|path|array[integer]| 是 |主键串|

> 返回示例

> 200 Response

```
{"code":0,"msg":"string","data":null}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[RVoid](#schemarvoid)|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|string|

# 业务模块/政策

<a id="opIdedit_1"></a>

## PUT 修改政策

PUT /biz/policy

修改政策

> Body 请求参数

```json
{
  "id": 0,
  "title": "string",
  "content": "string",
  "cover": 0,
  "attachment": "string",
  "url": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|[BizPolicyBo](#schemabizpolicybo)| 否 |none|

> 返回示例

> 200 Response

```
{"code":0,"msg":"string","data":null}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[RVoid](#schemarvoid)|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|string|

<a id="opIdadd_1"></a>

## POST 新增政策

POST /biz/policy

新增政策

> Body 请求参数

```json
{
  "id": 0,
  "title": "string",
  "content": "string",
  "cover": 0,
  "attachment": "string",
  "url": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|[BizPolicyBo](#schemabizpolicybo)| 否 |none|

> 返回示例

> 200 Response

```
{"code":0,"msg":"string","data":null}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[RVoid](#schemarvoid)|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|string|

<a id="opIdexport_1"></a>

## POST 导出政策列表

POST /biz/policy/export

导出政策列表

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|query|integer(int64)| 是 |id|
|title|query|string| 是 |标题|
|content|query|string| 否 |内容|
|cover|query|integer(int64)| 否 |封面|
|attachment|query|string| 否 |附件|
|url|query|string| 否 |链接|

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|None|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|None|

<a id="opIdgetInfo_1"></a>

## GET 获取政策详细信息

GET /biz/policy/{id}

获取政策详细信息

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|path|integer(int64)| 是 |主键|

> 返回示例

> 200 Response

```
{"code":0,"msg":"string","data":{"id":0,"title":"string","content":"string","cover":0,"coverUrl":0,"attachment":"string","attachmentUrl":"string","url":"string","createTime":"2019-08-24T14:15:22Z","createBy":"string"}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[RBizPolicyVo](#schemarbizpolicyvo)|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|string|

<a id="opIdlist_1"></a>

## GET 查询政策列表

GET /biz/policy/list

查询政策列表

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|query|integer(int64)| 是 |id|
|title|query|string| 是 |标题|
|content|query|string| 否 |内容|
|cover|query|integer(int64)| 否 |封面|
|attachment|query|string| 否 |附件|
|url|query|string| 否 |链接|
|pageSize|query|integer(int32)| 否 |分页大小|
|pageNum|query|integer(int32)| 否 |当前页数|
|orderByColumn|query|string| 否 |排序列|
|isAsc|query|string| 否 |排序的方向desc或者asc|

> 返回示例

> 200 Response

```
{"total":0,"rows":[{"id":0,"title":"string","content":"string","cover":0,"coverUrl":0,"attachment":"string","attachmentUrl":"string","url":"string","createTime":"2019-08-24T14:15:22Z","createBy":"string"}],"code":0,"msg":"string"}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[TableDataInfoBizPolicyVo](#schematabledatainfobizpolicyvo)|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|string|

<a id="opIdremove_1"></a>

## DELETE 删除政策

DELETE /biz/policy/{ids}

删除政策

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|ids|path|array[integer]| 是 |主键串|

> 返回示例

> 200 Response

```
{"code":0,"msg":"string","data":null}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[RVoid](#schemarvoid)|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|string|

# 业务模块/产业成果

<a id="opIdedit_2"></a>

## PUT 修改产业成果

PUT /biz/industryAchievement

修改产业成果

> Body 请求参数

```json
{
  "id": 0,
  "name": "string",
  "category": "string",
  "content": "string",
  "attachment": "string",
  "companyName": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|[BizIndustryAchievementBo](#schemabizindustryachievementbo)| 否 |none|

> 返回示例

> 200 Response

```
{"code":0,"msg":"string","data":null}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[RVoid](#schemarvoid)|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|string|

<a id="opIdadd_2"></a>

## POST 新增产业成果

POST /biz/industryAchievement

新增产业成果

> Body 请求参数

```json
{
  "id": 0,
  "name": "string",
  "category": "string",
  "content": "string",
  "attachment": "string",
  "companyName": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|[BizIndustryAchievementBo](#schemabizindustryachievementbo)| 否 |none|

> 返回示例

> 200 Response

```
{"code":0,"msg":"string","data":null}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[RVoid](#schemarvoid)|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|string|

<a id="opIdexport_2"></a>

## POST 导出产业成果列表

POST /biz/industryAchievement/export

导出产业成果列表

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|query|integer(int64)| 是 |id|
|name|query|string| 是 |名称|
|category|query|string| 是 |分类|
|content|query|string| 否 |内容|
|attachment|query|string| 是 |附件|
|companyName|query|string| 是 |公司名称|

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|None|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|None|

<a id="opIdgetInfo_2"></a>

## GET 获取产业成果详细信息

GET /biz/industryAchievement/{id}

获取产业成果详细信息

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|path|integer(int64)| 是 |主键|

> 返回示例

> 200 Response

```
{"code":0,"msg":"string","data":{"id":0,"name":"string","category":"string","content":"string","attachment":"string","attachmentUrl":"string","companyName":"string","createTime":"2019-08-24T14:15:22Z","createBy":"string"}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[RBizIndustryAchievementVo](#schemarbizindustryachievementvo)|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|string|

<a id="opIdlist_2"></a>

## GET 查询产业成果列表

GET /biz/industryAchievement/list

查询产业成果列表

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|query|integer(int64)| 是 |id|
|name|query|string| 是 |名称|
|category|query|string| 是 |分类|
|content|query|string| 否 |内容|
|attachment|query|string| 是 |附件|
|companyName|query|string| 是 |公司名称|
|pageSize|query|integer(int32)| 否 |分页大小|
|pageNum|query|integer(int32)| 否 |当前页数|
|orderByColumn|query|string| 否 |排序列|
|isAsc|query|string| 否 |排序的方向desc或者asc|

> 返回示例

> 200 Response

```
{"total":0,"rows":[{"id":0,"name":"string","category":"string","content":"string","attachment":"string","attachmentUrl":"string","companyName":"string","createTime":"2019-08-24T14:15:22Z","createBy":"string"}],"code":0,"msg":"string"}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[TableDataInfoBizIndustryAchievementVo](#schematabledatainfobizindustryachievementvo)|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|string|

<a id="opIdremove_2"></a>

## DELETE 删除产业成果

DELETE /biz/industryAchievement/{ids}

删除产业成果

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|ids|path|array[integer]| 是 |主键串|

> 返回示例

> 200 Response

```
{"code":0,"msg":"string","data":null}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[RVoid](#schemarvoid)|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|string|

# 业务模块/企业

<a id="opIdedit_3"></a>

## PUT 修改企业

PUT /biz/company

修改企业

> Body 请求参数

```json
{
  "id": 0,
  "name": "string",
  "area": "string",
  "contact": "string",
  "email": "string",
  "logo": 0,
  "mainBusiness": "string",
  "description": "string",
  "legalRepresentative": "string",
  "registeredCapital": 0.1,
  "establishmentTime": "2019-08-24T14:15:22Z",
  "creditCode": "string",
  "industry": "string",
  "financingList": [
    {
      "fundingTime": "2019-08-24T14:15:22Z",
      "fundingStage": "string",
      "fundingAmount": 0.1,
      "investor": "string"
    }
  ]
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|[BizCompanyBo](#schemabizcompanybo)| 否 |none|

> 返回示例

> 200 Response

```
{"code":0,"msg":"string","data":null}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[RVoid](#schemarvoid)|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|string|

<a id="opIdadd_3"></a>

## POST 新增企业

POST /biz/company

新增企业

> Body 请求参数

```json
{
  "id": 0,
  "name": "string",
  "area": "string",
  "contact": "string",
  "email": "string",
  "logo": 0,
  "mainBusiness": "string",
  "description": "string",
  "legalRepresentative": "string",
  "registeredCapital": 0.1,
  "establishmentTime": "2019-08-24T14:15:22Z",
  "creditCode": "string",
  "industry": "string",
  "financingList": [
    {
      "fundingTime": "2019-08-24T14:15:22Z",
      "fundingStage": "string",
      "fundingAmount": 0.1,
      "investor": "string"
    }
  ]
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|[BizCompanyBo](#schemabizcompanybo)| 否 |none|

> 返回示例

> 200 Response

```
{"code":0,"msg":"string","data":null}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[RVoid](#schemarvoid)|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|string|

<a id="opIdexport_3"></a>

## POST 导出企业列表

POST /biz/company/export

导出企业列表

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|query|integer(int64)| 是 |id|
|name|query|string| 是 |名称|
|area|query|string| 否 |所在地|
|contact|query|string| 否 |联系电话|
|email|query|string| 否 |邮箱|
|logo|query|integer(int64)| 是 |logo|
|mainBusiness|query|string| 否 |主营业务|
|description|query|string| 否 |简介|
|legalRepresentative|query|string| 否 |法人代表|
|registeredCapital|query|number(double)| 否 |注册资本|
|establishmentTime|query|string(date-time)| 否 |成立时间|
|creditCode|query|string| 否 |信用代码|
|industry|query|string| 否 |行业|
|financingList|query|array[object]| 否 |融资信息|

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|None|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|None|

<a id="opIdgetInfo_3"></a>

## GET 获取企业详细信息

GET /biz/company/{id}

获取企业详细信息

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|path|integer(int64)| 是 |主键|

> 返回示例

> 200 Response

```
{"code":0,"msg":"string","data":{"id":0,"name":"string","area":"string","contact":"string","email":"string","logo":0,"logoUrl":"string","mainBusiness":"string","description":"string","legalRepresentative":"string","registeredCapital":0.1,"establishmentTime":"2019-08-24T14:15:22Z","creditCode":"string","industry":"string","createTime":"2019-08-24T14:15:22Z","createBy":"string","financingList":[{"id":0,"fundingTime":"2019-08-24T14:15:22Z","fundingStage":"string","fundingAmount":0.1,"investor":"string"}]}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[RBizCompanyVo](#schemarbizcompanyvo)|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|string|

<a id="opIdlist_3"></a>

## GET 查询企业列表

GET /biz/company/list

查询企业列表

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|query|integer(int64)| 是 |id|
|name|query|string| 是 |名称|
|area|query|string| 否 |所在地|
|contact|query|string| 否 |联系电话|
|email|query|string| 否 |邮箱|
|logo|query|integer(int64)| 是 |logo|
|mainBusiness|query|string| 否 |主营业务|
|description|query|string| 否 |简介|
|legalRepresentative|query|string| 否 |法人代表|
|registeredCapital|query|number(double)| 否 |注册资本|
|establishmentTime|query|string(date-time)| 否 |成立时间|
|creditCode|query|string| 否 |信用代码|
|industry|query|string| 否 |行业|
|financingList|query|array[object]| 否 |融资信息|
|pageSize|query|integer(int32)| 否 |分页大小|
|pageNum|query|integer(int32)| 否 |当前页数|
|orderByColumn|query|string| 否 |排序列|
|isAsc|query|string| 否 |排序的方向desc或者asc|

> 返回示例

> 200 Response

```
{"total":0,"rows":[{"id":0,"name":"string","area":"string","contact":"string","email":"string","logo":0,"logoUrl":"string","mainBusiness":"string","description":"string","legalRepresentative":"string","registeredCapital":0.1,"establishmentTime":"2019-08-24T14:15:22Z","creditCode":"string","industry":"string","createTime":"2019-08-24T14:15:22Z","createBy":"string","financingList":[{"id":0,"fundingTime":"2019-08-24T14:15:22Z","fundingStage":"string","fundingAmount":0.1,"investor":"string"}]}],"code":0,"msg":"string"}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[TableDataInfoBizCompanyVo](#schematabledatainfobizcompanyvo)|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|string|

<a id="opIdremove_3"></a>

## DELETE 删除企业

DELETE /biz/company/{ids}

删除企业

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|ids|path|array[integer]| 是 |主键串|

> 返回示例

> 200 Response

```
{"code":0,"msg":"string","data":null}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[RVoid](#schemarvoid)|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|string|

# 数据模型

<h2 id="tocS_BizThirdPartyOrgBo">BizThirdPartyOrgBo</h2>

<a id="schemabizthirdpartyorgbo"></a>
<a id="schema_BizThirdPartyOrgBo"></a>
<a id="tocSbizthirdpartyorgbo"></a>
<a id="tocsbizthirdpartyorgbo"></a>

```json
{
  "id": 0,
  "name": "string",
  "logo": 0,
  "category": "string",
  "content": "string",
  "serviceScope": "string",
  "attachment": "string"
}

```

第三方机构业务对象 biz_third_party_org

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|true|none||id|
|name|string|true|none||名称|
|logo|integer(int64)|false|none||logo|
|category|string|true|none||分类|
|content|string|false|none||内容|
|serviceScope|string|false|none||服务范围|
|attachment|string|false|none||附件|

<h2 id="tocS_RVoid">RVoid</h2>

<a id="schemarvoid"></a>
<a id="schema_RVoid"></a>
<a id="tocSrvoid"></a>
<a id="tocsrvoid"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": null
}

```

响应信息主体

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|msg|string|false|none||none|
|data|any|false|none||none|

<h2 id="tocS_BizPolicyBo">BizPolicyBo</h2>

<a id="schemabizpolicybo"></a>
<a id="schema_BizPolicyBo"></a>
<a id="tocSbizpolicybo"></a>
<a id="tocsbizpolicybo"></a>

```json
{
  "id": 0,
  "title": "string",
  "content": "string",
  "cover": 0,
  "attachment": "string",
  "url": "string"
}

```

政策业务对象 biz_policy

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|true|none||id|
|title|string|true|none||标题|
|content|string|false|none||内容|
|cover|integer(int64)|false|none||封面|
|attachment|string|false|none||附件|
|url|string|false|none||链接|

<h2 id="tocS_BizIndustryAchievementBo">BizIndustryAchievementBo</h2>

<a id="schemabizindustryachievementbo"></a>
<a id="schema_BizIndustryAchievementBo"></a>
<a id="tocSbizindustryachievementbo"></a>
<a id="tocsbizindustryachievementbo"></a>

```json
{
  "id": 0,
  "name": "string",
  "category": "string",
  "content": "string",
  "attachment": "string",
  "companyName": "string"
}

```

产业成果业务对象 biz_industry_achievement

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|true|none||id|
|name|string|true|none||名称|
|category|string|true|none||分类|
|content|string|false|none||内容|
|attachment|string|true|none||附件|
|companyName|string|true|none||公司名称|

<h2 id="tocS_BizCompanyBo">BizCompanyBo</h2>

<a id="schemabizcompanybo"></a>
<a id="schema_BizCompanyBo"></a>
<a id="tocSbizcompanybo"></a>
<a id="tocsbizcompanybo"></a>

```json
{
  "id": 0,
  "name": "string",
  "area": "string",
  "contact": "string",
  "email": "string",
  "logo": 0,
  "mainBusiness": "string",
  "description": "string",
  "legalRepresentative": "string",
  "registeredCapital": 0.1,
  "establishmentTime": "2019-08-24T14:15:22Z",
  "creditCode": "string",
  "industry": "string",
  "financingList": [
    {
      "fundingTime": "2019-08-24T14:15:22Z",
      "fundingStage": "string",
      "fundingAmount": 0.1,
      "investor": "string"
    }
  ]
}

```

企业业务对象 biz_company

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|true|none||id|
|name|string|true|none||名称|
|area|string|false|none||所在地|
|contact|string|false|none||联系电话|
|email|string|false|none||邮箱|
|logo|integer(int64)|true|none||logo|
|mainBusiness|string|false|none||主营业务|
|description|string|false|none||简介|
|legalRepresentative|string|false|none||法人代表|
|registeredCapital|number(double)|false|none||注册资本|
|establishmentTime|string(date-time)|false|none||成立时间|
|creditCode|string|false|none||信用代码|
|industry|string|false|none||行业|
|financingList|[[BizFinancingBo](#schemabizfinancingbo)]|false|none||融资信息|

<h2 id="tocS_BizFinancingBo">BizFinancingBo</h2>

<a id="schemabizfinancingbo"></a>
<a id="schema_BizFinancingBo"></a>
<a id="tocSbizfinancingbo"></a>
<a id="tocsbizfinancingbo"></a>

```json
{
  "fundingTime": "2019-08-24T14:15:22Z",
  "fundingStage": "string",
  "fundingAmount": 0.1,
  "investor": "string"
}

```

融资信息业务对象 biz_financing

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|fundingTime|string(date-time)|false|none||融资时间|
|fundingStage|string|false|none||融资阶段|
|fundingAmount|number(double)|false|none||融资金额(万元)|
|investor|string|false|none||投资方|

<h2 id="tocS_BizThirdPartyOrgVo">BizThirdPartyOrgVo</h2>

<a id="schemabizthirdpartyorgvo"></a>
<a id="schema_BizThirdPartyOrgVo"></a>
<a id="tocSbizthirdpartyorgvo"></a>
<a id="tocsbizthirdpartyorgvo"></a>

```json
{
  "id": 0,
  "name": "string",
  "logo": 0,
  "logoUrl": "string",
  "category": "string",
  "content": "string",
  "serviceScope": "string",
  "attachment": "string",
  "attachmentUrl": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "createBy": "string"
}

```

第三方机构视图对象 biz_third_party_org

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||id|
|name|string|false|none||名称|
|logo|integer(int64)|false|none||logo|
|logoUrl|string|false|none||logo URL|
|category|string|false|none||分类|
|content|string|false|none||内容|
|serviceScope|string|false|none||服务范围|
|attachment|string|false|none||附件|
|attachmentUrl|string|false|none||附件URL|
|createTime|string(date-time)|false|none||创建时间|
|createBy|string|false|none||创建人|

<h2 id="tocS_RBizThirdPartyOrgVo">RBizThirdPartyOrgVo</h2>

<a id="schemarbizthirdpartyorgvo"></a>
<a id="schema_RBizThirdPartyOrgVo"></a>
<a id="tocSrbizthirdpartyorgvo"></a>
<a id="tocsrbizthirdpartyorgvo"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "id": 0,
    "name": "string",
    "logo": 0,
    "logoUrl": "string",
    "category": "string",
    "content": "string",
    "serviceScope": "string",
    "attachment": "string",
    "attachmentUrl": "string",
    "createTime": "2019-08-24T14:15:22Z",
    "createBy": "string"
  }
}

```

响应信息主体

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|msg|string|false|none||none|
|data|[BizThirdPartyOrgVo](#schemabizthirdpartyorgvo)|false|none||第三方机构视图对象 biz_third_party_org|

<h2 id="tocS_TableDataInfoBizThirdPartyOrgVo">TableDataInfoBizThirdPartyOrgVo</h2>

<a id="schematabledatainfobizthirdpartyorgvo"></a>
<a id="schema_TableDataInfoBizThirdPartyOrgVo"></a>
<a id="tocStabledatainfobizthirdpartyorgvo"></a>
<a id="tocstabledatainfobizthirdpartyorgvo"></a>

```json
{
  "total": 0,
  "rows": [
    {
      "id": 0,
      "name": "string",
      "logo": 0,
      "logoUrl": "string",
      "category": "string",
      "content": "string",
      "serviceScope": "string",
      "attachment": "string",
      "attachmentUrl": "string",
      "createTime": "2019-08-24T14:15:22Z",
      "createBy": "string"
    }
  ],
  "code": 0,
  "msg": "string"
}

```

表格分页数据对象

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|total|integer(int64)|false|none||总记录数|
|rows|[[BizThirdPartyOrgVo](#schemabizthirdpartyorgvo)]|false|none||列表数据|
|code|integer(int32)|false|none||消息状态码|
|msg|string|false|none||消息内容|

<h2 id="tocS_BizPolicyVo">BizPolicyVo</h2>

<a id="schemabizpolicyvo"></a>
<a id="schema_BizPolicyVo"></a>
<a id="tocSbizpolicyvo"></a>
<a id="tocsbizpolicyvo"></a>

```json
{
  "id": 0,
  "title": "string",
  "content": "string",
  "cover": 0,
  "coverUrl": 0,
  "attachment": "string",
  "attachmentUrl": "string",
  "url": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "createBy": "string"
}

```

政策视图对象 biz_policy

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||id|
|title|string|false|none||标题|
|content|string|false|none||内容|
|cover|integer(int64)|false|none||封面|
|coverUrl|integer(int64)|false|none||封面|
|attachment|string|false|none||附件|
|attachmentUrl|string|false|none||附件|
|url|string|false|none||链接|
|createTime|string(date-time)|false|none||创建时间|
|createBy|string|false|none||创建人|

<h2 id="tocS_RBizPolicyVo">RBizPolicyVo</h2>

<a id="schemarbizpolicyvo"></a>
<a id="schema_RBizPolicyVo"></a>
<a id="tocSrbizpolicyvo"></a>
<a id="tocsrbizpolicyvo"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "id": 0,
    "title": "string",
    "content": "string",
    "cover": 0,
    "coverUrl": 0,
    "attachment": "string",
    "attachmentUrl": "string",
    "url": "string",
    "createTime": "2019-08-24T14:15:22Z",
    "createBy": "string"
  }
}

```

响应信息主体

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|msg|string|false|none||none|
|data|[BizPolicyVo](#schemabizpolicyvo)|false|none||政策视图对象 biz_policy|

<h2 id="tocS_TableDataInfoBizPolicyVo">TableDataInfoBizPolicyVo</h2>

<a id="schematabledatainfobizpolicyvo"></a>
<a id="schema_TableDataInfoBizPolicyVo"></a>
<a id="tocStabledatainfobizpolicyvo"></a>
<a id="tocstabledatainfobizpolicyvo"></a>

```json
{
  "total": 0,
  "rows": [
    {
      "id": 0,
      "title": "string",
      "content": "string",
      "cover": 0,
      "coverUrl": 0,
      "attachment": "string",
      "attachmentUrl": "string",
      "url": "string",
      "createTime": "2019-08-24T14:15:22Z",
      "createBy": "string"
    }
  ],
  "code": 0,
  "msg": "string"
}

```

表格分页数据对象

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|total|integer(int64)|false|none||总记录数|
|rows|[[BizPolicyVo](#schemabizpolicyvo)]|false|none||列表数据|
|code|integer(int32)|false|none||消息状态码|
|msg|string|false|none||消息内容|

<h2 id="tocS_BizIndustryAchievementVo">BizIndustryAchievementVo</h2>

<a id="schemabizindustryachievementvo"></a>
<a id="schema_BizIndustryAchievementVo"></a>
<a id="tocSbizindustryachievementvo"></a>
<a id="tocsbizindustryachievementvo"></a>

```json
{
  "id": 0,
  "name": "string",
  "category": "string",
  "content": "string",
  "attachment": "string",
  "attachmentUrl": "string",
  "companyName": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "createBy": "string"
}

```

产业成果视图对象 biz_industry_achievement

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||id|
|name|string|false|none||名称|
|category|string|false|none||分类|
|content|string|false|none||内容|
|attachment|string|false|none||附件|
|attachmentUrl|string|false|none||附件URL|
|companyName|string|false|none||公司名称|
|createTime|string(date-time)|false|none||创建时间|
|createBy|string|false|none||创建人|

<h2 id="tocS_RBizIndustryAchievementVo">RBizIndustryAchievementVo</h2>

<a id="schemarbizindustryachievementvo"></a>
<a id="schema_RBizIndustryAchievementVo"></a>
<a id="tocSrbizindustryachievementvo"></a>
<a id="tocsrbizindustryachievementvo"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "id": 0,
    "name": "string",
    "category": "string",
    "content": "string",
    "attachment": "string",
    "attachmentUrl": "string",
    "companyName": "string",
    "createTime": "2019-08-24T14:15:22Z",
    "createBy": "string"
  }
}

```

响应信息主体

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|msg|string|false|none||none|
|data|[BizIndustryAchievementVo](#schemabizindustryachievementvo)|false|none||产业成果视图对象 biz_industry_achievement|

<h2 id="tocS_TableDataInfoBizIndustryAchievementVo">TableDataInfoBizIndustryAchievementVo</h2>

<a id="schematabledatainfobizindustryachievementvo"></a>
<a id="schema_TableDataInfoBizIndustryAchievementVo"></a>
<a id="tocStabledatainfobizindustryachievementvo"></a>
<a id="tocstabledatainfobizindustryachievementvo"></a>

```json
{
  "total": 0,
  "rows": [
    {
      "id": 0,
      "name": "string",
      "category": "string",
      "content": "string",
      "attachment": "string",
      "attachmentUrl": "string",
      "companyName": "string",
      "createTime": "2019-08-24T14:15:22Z",
      "createBy": "string"
    }
  ],
  "code": 0,
  "msg": "string"
}

```

表格分页数据对象

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|total|integer(int64)|false|none||总记录数|
|rows|[[BizIndustryAchievementVo](#schemabizindustryachievementvo)]|false|none||列表数据|
|code|integer(int32)|false|none||消息状态码|
|msg|string|false|none||消息内容|

<h2 id="tocS_BizCompanyVo">BizCompanyVo</h2>

<a id="schemabizcompanyvo"></a>
<a id="schema_BizCompanyVo"></a>
<a id="tocSbizcompanyvo"></a>
<a id="tocsbizcompanyvo"></a>

```json
{
  "id": 0,
  "name": "string",
  "area": "string",
  "contact": "string",
  "email": "string",
  "logo": 0,
  "logoUrl": "string",
  "mainBusiness": "string",
  "description": "string",
  "legalRepresentative": "string",
  "registeredCapital": 0.1,
  "establishmentTime": "2019-08-24T14:15:22Z",
  "creditCode": "string",
  "industry": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "createBy": "string",
  "financingList": [
    {
      "id": 0,
      "fundingTime": "2019-08-24T14:15:22Z",
      "fundingStage": "string",
      "fundingAmount": 0.1,
      "investor": "string"
    }
  ]
}

```

企业视图对象 biz_company

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||id|
|name|string|false|none||名称|
|area|string|false|none||所在地|
|contact|string|false|none||联系电话|
|email|string|false|none||邮箱|
|logo|integer(int64)|false|none||logo|
|logoUrl|string|false|none||logo URL|
|mainBusiness|string|false|none||主营业务|
|description|string|false|none||简介|
|legalRepresentative|string|false|none||法人代表|
|registeredCapital|number(double)|false|none||注册资本|
|establishmentTime|string(date-time)|false|none||成立时间|
|creditCode|string|false|none||信用代码|
|industry|string|false|none||行业|
|createTime|string(date-time)|false|none||创建时间|
|createBy|string|false|none||创建人|
|financingList|[[BizFinancingVo](#schemabizfinancingvo)]|false|none||融资信息|

<h2 id="tocS_BizFinancingVo">BizFinancingVo</h2>

<a id="schemabizfinancingvo"></a>
<a id="schema_BizFinancingVo"></a>
<a id="tocSbizfinancingvo"></a>
<a id="tocsbizfinancingvo"></a>

```json
{
  "id": 0,
  "fundingTime": "2019-08-24T14:15:22Z",
  "fundingStage": "string",
  "fundingAmount": 0.1,
  "investor": "string"
}

```

融资信息视图对象 biz_financing

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||id|
|fundingTime|string(date-time)|false|none||融资时间|
|fundingStage|string|false|none||融资阶段|
|fundingAmount|number(double)|false|none||融资金额(万元)|
|investor|string|false|none||投资方|

<h2 id="tocS_RBizCompanyVo">RBizCompanyVo</h2>

<a id="schemarbizcompanyvo"></a>
<a id="schema_RBizCompanyVo"></a>
<a id="tocSrbizcompanyvo"></a>
<a id="tocsrbizcompanyvo"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "id": 0,
    "name": "string",
    "area": "string",
    "contact": "string",
    "email": "string",
    "logo": 0,
    "logoUrl": "string",
    "mainBusiness": "string",
    "description": "string",
    "legalRepresentative": "string",
    "registeredCapital": 0.1,
    "establishmentTime": "2019-08-24T14:15:22Z",
    "creditCode": "string",
    "industry": "string",
    "createTime": "2019-08-24T14:15:22Z",
    "createBy": "string",
    "financingList": [
      {
        "id": 0,
        "fundingTime": "2019-08-24T14:15:22Z",
        "fundingStage": "string",
        "fundingAmount": 0.1,
        "investor": "string"
      }
    ]
  }
}

```

响应信息主体

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|msg|string|false|none||none|
|data|[BizCompanyVo](#schemabizcompanyvo)|false|none||企业视图对象 biz_company|

<h2 id="tocS_TableDataInfoBizCompanyVo">TableDataInfoBizCompanyVo</h2>

<a id="schematabledatainfobizcompanyvo"></a>
<a id="schema_TableDataInfoBizCompanyVo"></a>
<a id="tocStabledatainfobizcompanyvo"></a>
<a id="tocstabledatainfobizcompanyvo"></a>

```json
{
  "total": 0,
  "rows": [
    {
      "id": 0,
      "name": "string",
      "area": "string",
      "contact": "string",
      "email": "string",
      "logo": 0,
      "logoUrl": "string",
      "mainBusiness": "string",
      "description": "string",
      "legalRepresentative": "string",
      "registeredCapital": 0.1,
      "establishmentTime": "2019-08-24T14:15:22Z",
      "creditCode": "string",
      "industry": "string",
      "createTime": "2019-08-24T14:15:22Z",
      "createBy": "string",
      "financingList": [
        {
          "id": 0,
          "fundingTime": "2019-08-24T14:15:22Z",
          "fundingStage": "string",
          "fundingAmount": 0.1,
          "investor": "string"
        }
      ]
    }
  ],
  "code": 0,
  "msg": "string"
}

```

表格分页数据对象

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|total|integer(int64)|false|none||总记录数|
|rows|[[BizCompanyVo](#schemabizcompanyvo)]|false|none||列表数据|
|code|integer(int32)|false|none||消息状态码|
|msg|string|false|none||消息内容|

