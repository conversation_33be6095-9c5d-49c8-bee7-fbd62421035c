<route lang="jsonc" type="page">
{
  "style": {
    "navigationBarTitleText": "需求大厅"
  }
}
</route>

<script setup lang="ts">
import { onLoad } from '@dcloudio/uni-app'
import { ref } from 'vue'

// 技术需求数据接口
interface TechDemand {
  id: number
  requester: string
  content: string
  time: string
  category: string
  budget: string
  contact: string
  status: string
}

const demandList = ref<TechDemand[]>([])

// 模拟技术需求数据
const demandData: TechDemand[] = [
  {
    id: 1,
    requester: '张工程师',
    content: '寻求工业自动化控制系统的技术合作，需要具备PLC编程和机器视觉集成经验',
    time: '2小时前',
    category: '工业自动化',
    budget: '10-20万',
    contact: '张工 - 13800138000',
    status: '待对接',
  },
  {
    id: 2,
    requester: '李技术总监',
    content: '需要AI图像识别算法的技术支援，用于产品质量检测系统开发',
    time: '5小时前',
    category: '人工智能',
    budget: '15-30万',
    contact: '李总 - 13900139000',
    status: '洽谈中',
  },
  {
    id: 3,
    requester: '王项目经理',
    content: '寻找新能源电池材料研发合作伙伴，要求有锂离子电池正极材料研发经验',
    time: '1天前',
    category: '新能源',
    budget: '50-100万',
    contact: '王经理 - 13700137000',
    status: '待对接',
  },
  {
    id: 4,
    requester: '陈研发主管',
    content: '需要物联网设备数据采集和云端传输的技术方案',
    time: '2天前',
    category: '物联网',
    budget: '20-40万',
    contact: '陈主管 - 13600136000',
    status: '已对接',
  },
  {
    id: 5,
    requester: '刘产品经理',
    content: '寻求智能家居APP开发团队，要求有跨平台开发经验',
    time: '3天前',
    category: '软件开发',
    budget: '30-60万',
    contact: '刘经理 - 13500135000',
    status: '洽谈中',
  },
]

onLoad(() => {
  demandList.value = demandData
})

// 跳转到需求详情页面
function goToDemandDetail(id: number) {
  uni.navigateTo({
    url: `/pages/demand-hall/detail?id=${id}`,
  })
}

// 返回上一页
function goBack() {
  uni.navigateBack()
}
</script>

<template>
  <view class="min-h-screen bg-white pb-20">
    <!-- 返回按钮 -->
    <view class="px-4 pt-4">
      <wd-icon name="arrow-left" size="20" @click="goBack" />
    </view>

    <!-- 页面标题 -->
    <view class="px-4 pt-2">
      <text class="text-xl font-bold">
        技术需求列表
      </text>
    </view>

    <!-- 技术需求列表 -->
    <view class="px-4 pt-4">
      <view class="demand-list space-y-3">
        <view
          v-for="demand in demandList"
          :key="demand.id"
          class="demand-item rounded-lg bg-gray-50 p-4"
          @click="goToDemandDetail(demand.id)"
        >
          <view class="mb-3 flex items-center justify-between">
            <text class="text-base text-blue-600 font-medium">
              {{ demand.requester }}
            </text>
            <text class="text-xs text-gray-500">
              {{ demand.time }}
            </text>
          </view>

          <text class="line-clamp-2 mb-2 block text-sm text-gray-800">
            {{ demand.content }}
          </text>

          <view class="flex items-center justify-between">
            <view class="flex items-center space-x-2">
              <text class="rounded bg-blue-100 px-2 py-1 text-xs text-blue-700">
                {{ demand.category }}
              </text>
              <text class="text-xs text-gray-600">
                预算: {{ demand.budget }}
              </text>
            </view>
            <text
              :class="{
                'text-green-600': demand.status === '已对接',
                'text-blue-600': demand.status === '洽谈中',
                'text-orange-600': demand.status === '待对接',
              }"
              class="text-xs font-medium"
            >
              {{ demand.status }}
            </text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.demand-item {
  transition: all 0.2s ease;

  &:active {
    background-color: #e5e7eb;
    transform: scale(0.98);
  }
}

// 文本溢出处理
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
