{"apifoxProject": "1.0.0", "$schema": {"app": "apifox", "type": "project", "version": "1.2.0"}, "info": {"name": "科创通", "description": "", "mockRule": {"rules": [], "enableSystemRule": true}}, "apiCollection": [{"name": "根目录", "id": 65684386, "auth": {}, "securityScheme": {}, "parentId": 0, "serverId": "", "description": "", "identityPattern": {"httpApi": {"type": "methodAndPath", "bodyType": "", "fields": []}}, "shareSettings": {}, "visibility": "SHARED", "moduleId": 6116404, "preProcessors": [{"id": "inheritProcessors", "type": "inheritProcessors", "data": {}}], "postProcessors": [{"id": "inheritProcessors", "type": "inheritProcessors", "data": {}}], "inheritPostProcessors": {}, "inheritPreProcessors": {}, "items": [{"name": "业务模块", "id": 65684602, "auth": {}, "securityScheme": {}, "parentId": 0, "serverId": "", "description": "", "identityPattern": {"httpApi": {"type": "inherit", "bodyType": "", "fields": []}}, "shareSettings": {}, "visibility": "INHERITED", "moduleId": 6116404, "preProcessors": [{"id": "inheritProcessors", "type": "inheritProcessors", "data": {}}], "postProcessors": [{"id": "inheritProcessors", "type": "inheritProcessors", "data": {}}], "inheritPostProcessors": {}, "inheritPreProcessors": {}, "items": [{"name": "第三方机构", "id": 65847309, "auth": {}, "securityScheme": {}, "parentId": 65684602, "serverId": "", "description": "", "identityPattern": {"httpApi": {"type": "inherit", "bodyType": "", "fields": []}}, "shareSettings": {}, "visibility": "INHERITED", "moduleId": 6116404, "preProcessors": [{"id": "inheritProcessors", "type": "inheritProcessors", "data": {}}], "postProcessors": [{"id": "inheritProcessors", "type": "inheritProcessors", "data": {}}], "inheritPostProcessors": {}, "inheritPreProcessors": {}, "items": [{"name": "修改第三方机构", "api": {"id": "346007943", "method": "put", "path": "/biz/thirdPartyOrg", "parameters": {"path": [], "query": [], "cookie": [], "header": []}, "auth": {}, "securityScheme": {}, "commonParameters": {}, "responses": [{"id": "761901303", "code": 200, "name": "成功", "headers": [], "jsonSchema": {"$ref": "#/definitions/*********"}, "description": "OK", "contentType": "json", "mediaType": "*/*", "oasExtensions": ""}, {"id": "761901304", "code": 401, "name": "没有权限", "headers": [], "jsonSchema": {"type": "string"}, "description": "Unauthorized", "contentType": "json", "mediaType": "*/*", "oasExtensions": ""}], "responseExamples": [], "requestBody": {"type": "application/json", "parameters": [], "jsonSchema": {"$ref": "#/definitions/199174970"}, "oasExtensions": ""}, "description": "修改第三方机构", "tags": ["第三方机构"], "status": "released", "serverId": "", "operationId": "edit", "sourceUrl": "", "ordering": 0, "cases": [], "mocks": [], "customApiFields": "{}", "advancedSettings": {"disabledSystemHeaders": {}}, "mockScript": {}, "codeSamples": [], "commonResponseStatus": {}, "responseChildren": [], "visibility": "INHERITED", "moduleId": 6116404, "oasExtensions": "", "type": "http", "preProcessors": [], "postProcessors": [], "inheritPostProcessors": {}, "inheritPreProcessors": {}}}, {"name": "新增第三方机构", "api": {"id": "346007944", "method": "post", "path": "/biz/thirdPartyOrg", "parameters": {"path": [], "query": [], "cookie": [], "header": []}, "auth": {}, "securityScheme": {}, "commonParameters": {}, "responses": [{"id": "761901305", "code": 200, "name": "成功", "headers": [], "jsonSchema": {"$ref": "#/definitions/*********"}, "description": "OK", "contentType": "json", "mediaType": "*/*", "oasExtensions": ""}, {"id": "761901306", "code": 401, "name": "没有权限", "headers": [], "jsonSchema": {"type": "string"}, "description": "Unauthorized", "contentType": "json", "mediaType": "*/*", "oasExtensions": ""}], "responseExamples": [], "requestBody": {"type": "application/json", "parameters": [], "jsonSchema": {"$ref": "#/definitions/199174970"}, "oasExtensions": ""}, "description": "新增第三方机构", "tags": ["第三方机构"], "status": "released", "serverId": "", "operationId": "add", "sourceUrl": "", "ordering": 6, "cases": [], "mocks": [], "customApiFields": "{}", "advancedSettings": {"disabledSystemHeaders": {}}, "mockScript": {}, "codeSamples": [], "commonResponseStatus": {}, "responseChildren": [], "visibility": "INHERITED", "moduleId": 6116404, "oasExtensions": "", "type": "http", "preProcessors": [], "postProcessors": [], "inheritPostProcessors": {}, "inheritPreProcessors": {}}}, {"name": "导出第三方机构列表", "api": {"id": "346007945", "method": "post", "path": "/biz/thirdPartyOrg/export", "parameters": {"path": [], "query": [{"id": "F5UTKTN5jN", "name": "id", "required": true, "description": "id", "example": "", "type": "integer", "schema": {"type": "integer", "format": "int64", "description": "id"}}, {"id": "BRCblLa3LF", "name": "name", "required": true, "description": "名称", "example": "", "type": "string", "schema": {"type": "string", "description": "名称", "minLength": 1}}, {"id": "QQmx8HvK3l", "name": "logo", "required": false, "description": "logo", "example": "", "type": "integer", "schema": {"type": "integer", "format": "int64", "description": "logo"}}, {"id": "5cUeMunJXF", "name": "category", "required": true, "description": "分类", "example": "", "type": "string", "schema": {"type": "string", "description": "分类", "minLength": 1}}, {"id": "UHxC6orVPu", "name": "content", "required": false, "description": "内容", "example": "", "type": "string", "schema": {"type": "string", "description": "内容"}}, {"id": "MBOsA47nZI", "name": "serviceScope", "required": false, "description": "服务范围", "example": "", "type": "string", "schema": {"type": "string", "description": "服务范围"}}, {"id": "BVvWwodwiQ", "name": "attachment", "required": false, "description": "附件", "example": "", "type": "string", "schema": {"type": "string", "description": "附件"}}], "cookie": [], "header": []}, "auth": {}, "securityScheme": {}, "commonParameters": {}, "responses": [{"id": "761901307", "code": 200, "name": "成功", "headers": [], "jsonSchema": {"type": "object", "properties": {}}, "description": "OK", "contentType": "noContent", "mediaType": "", "oasExtensions": ""}, {"id": "761901308", "code": 401, "name": "没有权限", "headers": [], "jsonSchema": {"type": "object", "properties": {}}, "description": "Unauthorized", "contentType": "noContent", "mediaType": "", "oasExtensions": ""}], "responseExamples": [], "requestBody": {"type": "none", "parameters": [], "oasExtensions": ""}, "description": "导出第三方机构列表", "tags": ["第三方机构"], "status": "released", "serverId": "", "operationId": "export", "sourceUrl": "", "ordering": 12, "cases": [], "mocks": [], "customApiFields": "{}", "advancedSettings": {"disabledSystemHeaders": {}}, "mockScript": {}, "codeSamples": [], "commonResponseStatus": {}, "responseChildren": [], "visibility": "INHERITED", "moduleId": 6116404, "oasExtensions": "", "type": "http", "preProcessors": [], "postProcessors": [], "inheritPostProcessors": {}, "inheritPreProcessors": {}}}, {"name": "获取第三方机构详细信息", "api": {"id": "346007946", "method": "get", "path": "/biz/thirdPartyOrg/{id}", "parameters": {"path": [{"id": "id#0", "name": "id", "required": true, "description": "主键", "example": "", "type": "integer", "schema": {"type": "integer", "format": "int64"}}], "query": [], "cookie": [], "header": []}, "auth": {}, "securityScheme": {}, "commonParameters": {}, "responses": [{"id": "761901309", "code": 200, "name": "成功", "headers": [], "jsonSchema": {"$ref": "#/definitions/199174976"}, "description": "OK", "contentType": "json", "mediaType": "*/*", "oasExtensions": ""}, {"id": "761901310", "code": 401, "name": "没有权限", "headers": [], "jsonSchema": {"type": "string"}, "description": "Unauthorized", "contentType": "json", "mediaType": "*/*", "oasExtensions": ""}], "responseExamples": [], "requestBody": {"type": "none", "parameters": [], "oasExtensions": ""}, "description": "获取第三方机构详细信息", "tags": ["第三方机构"], "status": "released", "serverId": "", "operationId": "getInfo", "sourceUrl": "", "ordering": 18, "cases": [], "mocks": [], "customApiFields": "{}", "advancedSettings": {"disabledSystemHeaders": {}}, "mockScript": {}, "codeSamples": [], "commonResponseStatus": {}, "responseChildren": [], "visibility": "INHERITED", "moduleId": 6116404, "oasExtensions": "", "type": "http", "preProcessors": [], "postProcessors": [], "inheritPostProcessors": {}, "inheritPreProcessors": {}}}, {"name": "查询第三方机构列表", "api": {"id": "346007947", "method": "get", "path": "/biz/thirdPartyOrg/list", "parameters": {"path": [], "query": [{"id": "KrBd6ctNxn", "name": "id", "required": true, "description": "id", "example": "", "type": "integer", "schema": {"type": "integer", "format": "int64", "description": "id"}}, {"id": "AtCeyDXqWU", "name": "name", "required": true, "description": "名称", "example": "", "type": "string", "schema": {"type": "string", "description": "名称", "minLength": 1}}, {"id": "GS5RSJ4UYO", "name": "logo", "required": false, "description": "logo", "example": "", "type": "integer", "schema": {"type": "integer", "format": "int64", "description": "logo"}}, {"id": "srjhK9bCTi", "name": "category", "required": true, "description": "分类", "example": "", "type": "string", "schema": {"type": "string", "description": "分类", "minLength": 1}}, {"id": "pM8rf4UClT", "name": "content", "required": false, "description": "内容", "example": "", "type": "string", "schema": {"type": "string", "description": "内容"}}, {"id": "QJX8cYPBYR", "name": "serviceScope", "required": false, "description": "服务范围", "example": "", "type": "string", "schema": {"type": "string", "description": "服务范围"}}, {"id": "g9jN17AHWl", "name": "attachment", "required": false, "description": "附件", "example": "", "type": "string", "schema": {"type": "string", "description": "附件"}}, {"id": "5FAhApoGmJ", "name": "pageSize", "required": false, "description": "分页大小", "example": "", "type": "integer", "schema": {"type": "integer", "format": "int32", "description": "分页大小"}}, {"id": "rZk5uJKDno", "name": "pageNum", "required": false, "description": "当前页数", "example": "", "type": "integer", "schema": {"type": "integer", "format": "int32", "description": "当前页数"}}, {"id": "gg8DgulzIX", "name": "orderByColumn", "required": false, "description": "排序列", "example": "", "type": "string", "schema": {"type": "string", "description": "排序列"}}, {"id": "EKjkX5CU8t", "name": "isAsc", "required": false, "description": "排序的方向desc或者asc", "example": "", "type": "string", "schema": {"type": "string", "description": "排序的方向desc或者asc"}}], "cookie": [], "header": []}, "auth": {}, "securityScheme": {}, "commonParameters": {}, "responses": [{"id": "761901311", "code": 200, "name": "成功", "headers": [], "jsonSchema": {"$ref": "#/definitions/199174977"}, "description": "OK", "contentType": "json", "mediaType": "*/*", "oasExtensions": ""}, {"id": "761901312", "code": 401, "name": "没有权限", "headers": [], "jsonSchema": {"type": "string"}, "description": "Unauthorized", "contentType": "json", "mediaType": "*/*", "oasExtensions": ""}], "responseExamples": [], "requestBody": {"type": "none", "parameters": [], "oasExtensions": ""}, "description": "查询第三方机构列表", "tags": ["第三方机构"], "status": "released", "serverId": "", "operationId": "list", "sourceUrl": "", "ordering": 24, "cases": [], "mocks": [], "customApiFields": "{}", "advancedSettings": {"disabledSystemHeaders": {}}, "mockScript": {}, "codeSamples": [], "commonResponseStatus": {}, "responseChildren": [], "visibility": "INHERITED", "moduleId": 6116404, "oasExtensions": "", "type": "http", "preProcessors": [], "postProcessors": [], "inheritPostProcessors": {}, "inheritPreProcessors": {}}}, {"name": "删除第三方机构", "api": {"id": "346007948", "method": "delete", "path": "/biz/thirdPartyOrg/{ids}", "parameters": {"path": [{"id": "ids#0", "name": "ids", "required": true, "description": "主键串", "example": "", "type": "array", "schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}], "query": [], "cookie": [], "header": []}, "auth": {}, "securityScheme": {}, "commonParameters": {}, "responses": [{"id": "761901313", "code": 200, "name": "成功", "headers": [], "jsonSchema": {"$ref": "#/definitions/*********"}, "description": "OK", "contentType": "json", "mediaType": "*/*", "oasExtensions": ""}, {"id": "761901314", "code": 401, "name": "没有权限", "headers": [], "jsonSchema": {"type": "string"}, "description": "Unauthorized", "contentType": "json", "mediaType": "*/*", "oasExtensions": ""}], "responseExamples": [], "requestBody": {"type": "none", "parameters": [], "oasExtensions": ""}, "description": "删除第三方机构", "tags": ["第三方机构"], "status": "released", "serverId": "", "operationId": "remove", "sourceUrl": "", "ordering": 30, "cases": [], "mocks": [], "customApiFields": "{}", "advancedSettings": {"disabledSystemHeaders": {}}, "mockScript": {}, "codeSamples": [], "commonResponseStatus": {}, "responseChildren": [], "visibility": "INHERITED", "moduleId": 6116404, "oasExtensions": "", "type": "http", "preProcessors": [], "postProcessors": [], "inheritPostProcessors": {}, "inheritPreProcessors": {}}}]}, {"name": "政策", "id": 65847310, "auth": {}, "securityScheme": {}, "parentId": 65684602, "serverId": "", "description": "", "identityPattern": {"httpApi": {"type": "inherit", "bodyType": "", "fields": []}}, "shareSettings": {}, "visibility": "INHERITED", "moduleId": 6116404, "preProcessors": [{"id": "inheritProcessors", "type": "inheritProcessors", "data": {}}], "postProcessors": [{"id": "inheritProcessors", "type": "inheritProcessors", "data": {}}], "inheritPostProcessors": {}, "inheritPreProcessors": {}, "items": [{"name": "修改政策", "api": {"id": "346007949", "method": "put", "path": "/biz/policy", "parameters": {"path": [], "query": [], "cookie": [], "header": []}, "auth": {}, "securityScheme": {}, "commonParameters": {}, "responses": [{"id": "761901315", "code": 200, "name": "成功", "headers": [], "jsonSchema": {"$ref": "#/definitions/*********"}, "description": "OK", "contentType": "json", "mediaType": "*/*", "oasExtensions": ""}, {"id": "761901316", "code": 401, "name": "没有权限", "headers": [], "jsonSchema": {"type": "string"}, "description": "Unauthorized", "contentType": "json", "mediaType": "*/*", "oasExtensions": ""}], "responseExamples": [], "requestBody": {"type": "application/json", "parameters": [], "jsonSchema": {"$ref": "#/definitions/199174971"}, "oasExtensions": ""}, "description": "修改政策", "tags": ["政策"], "status": "released", "serverId": "", "operationId": "edit_1", "sourceUrl": "", "ordering": 0, "cases": [], "mocks": [], "customApiFields": "{}", "advancedSettings": {"disabledSystemHeaders": {}}, "mockScript": {}, "codeSamples": [], "commonResponseStatus": {}, "responseChildren": [], "visibility": "INHERITED", "moduleId": 6116404, "oasExtensions": "", "type": "http", "preProcessors": [], "postProcessors": [], "inheritPostProcessors": {}, "inheritPreProcessors": {}}}, {"name": "新增政策", "api": {"id": "346007950", "method": "post", "path": "/biz/policy", "parameters": {"path": [], "query": [], "cookie": [], "header": []}, "auth": {}, "securityScheme": {}, "commonParameters": {}, "responses": [{"id": "761901317", "code": 200, "name": "成功", "headers": [], "jsonSchema": {"$ref": "#/definitions/*********"}, "description": "OK", "contentType": "json", "mediaType": "*/*", "oasExtensions": ""}, {"id": "761901318", "code": 401, "name": "没有权限", "headers": [], "jsonSchema": {"type": "string"}, "description": "Unauthorized", "contentType": "json", "mediaType": "*/*", "oasExtensions": ""}], "responseExamples": [], "requestBody": {"type": "application/json", "parameters": [], "jsonSchema": {"$ref": "#/definitions/199174971"}, "oasExtensions": ""}, "description": "新增政策", "tags": ["政策"], "status": "released", "serverId": "", "operationId": "add_1", "sourceUrl": "", "ordering": 6, "cases": [], "mocks": [], "customApiFields": "{}", "advancedSettings": {"disabledSystemHeaders": {}}, "mockScript": {}, "codeSamples": [], "commonResponseStatus": {}, "responseChildren": [], "visibility": "INHERITED", "moduleId": 6116404, "oasExtensions": "", "type": "http", "preProcessors": [], "postProcessors": [], "inheritPostProcessors": {}, "inheritPreProcessors": {}}}, {"name": "导出政策列表", "api": {"id": "346007951", "method": "post", "path": "/biz/policy/export", "parameters": {"path": [], "query": [{"id": "1uIEYqhp38", "name": "id", "required": true, "description": "id", "example": "", "type": "integer", "schema": {"type": "integer", "format": "int64", "description": "id"}}, {"id": "dcEj0W5kJQ", "name": "title", "required": true, "description": "标题", "example": "", "type": "string", "schema": {"type": "string", "description": "标题", "minLength": 1}}, {"id": "i8YRuldogQ", "name": "content", "required": false, "description": "内容", "example": "", "type": "string", "schema": {"type": "string", "description": "内容"}}, {"id": "q8g9gwnli0", "name": "cover", "required": false, "description": "封面", "example": "", "type": "integer", "schema": {"type": "integer", "format": "int64", "description": "封面"}}, {"id": "sKZCYN7L4Q", "name": "attachment", "required": false, "description": "附件", "example": "", "type": "string", "schema": {"type": "string", "description": "附件"}}, {"id": "jl8s37k5Xk", "name": "url", "required": false, "description": "链接", "example": "", "type": "string", "schema": {"type": "string", "description": "链接"}}], "cookie": [], "header": []}, "auth": {}, "securityScheme": {}, "commonParameters": {}, "responses": [{"id": "761901319", "code": 200, "name": "成功", "headers": [], "jsonSchema": {"type": "object", "properties": {}}, "description": "OK", "contentType": "noContent", "mediaType": "", "oasExtensions": ""}, {"id": "761901320", "code": 401, "name": "没有权限", "headers": [], "jsonSchema": {"type": "object", "properties": {}}, "description": "Unauthorized", "contentType": "noContent", "mediaType": "", "oasExtensions": ""}], "responseExamples": [], "requestBody": {"type": "none", "parameters": [], "oasExtensions": ""}, "description": "导出政策列表", "tags": ["政策"], "status": "released", "serverId": "", "operationId": "export_1", "sourceUrl": "", "ordering": 12, "cases": [], "mocks": [], "customApiFields": "{}", "advancedSettings": {"disabledSystemHeaders": {}}, "mockScript": {}, "codeSamples": [], "commonResponseStatus": {}, "responseChildren": [], "visibility": "INHERITED", "moduleId": 6116404, "oasExtensions": "", "type": "http", "preProcessors": [], "postProcessors": [], "inheritPostProcessors": {}, "inheritPreProcessors": {}}}, {"name": "获取政策详细信息", "api": {"id": "346007952", "method": "get", "path": "/biz/policy/{id}", "parameters": {"path": [{"id": "id#0", "name": "id", "required": true, "description": "主键", "example": "", "type": "integer", "schema": {"type": "integer", "format": "int64"}}], "query": [], "cookie": [], "header": []}, "auth": {}, "securityScheme": {}, "commonParameters": {}, "responses": [{"id": "761901321", "code": 200, "name": "成功", "headers": [], "jsonSchema": {"$ref": "#/definitions/199174979"}, "description": "OK", "contentType": "json", "mediaType": "*/*", "oasExtensions": ""}, {"id": "761901322", "code": 401, "name": "没有权限", "headers": [], "jsonSchema": {"type": "string"}, "description": "Unauthorized", "contentType": "json", "mediaType": "*/*", "oasExtensions": ""}], "responseExamples": [], "requestBody": {"type": "none", "parameters": [], "oasExtensions": ""}, "description": "获取政策详细信息", "tags": ["政策"], "status": "released", "serverId": "", "operationId": "getInfo_1", "sourceUrl": "", "ordering": 18, "cases": [], "mocks": [], "customApiFields": "{}", "advancedSettings": {"disabledSystemHeaders": {}}, "mockScript": {}, "codeSamples": [], "commonResponseStatus": {}, "responseChildren": [], "visibility": "INHERITED", "moduleId": 6116404, "oasExtensions": "", "type": "http", "preProcessors": [], "postProcessors": [], "inheritPostProcessors": {}, "inheritPreProcessors": {}}}, {"name": "查询政策列表", "api": {"id": "346007953", "method": "get", "path": "/biz/policy/list", "parameters": {"path": [], "query": [{"id": "7tQKT4U1km", "name": "id", "required": true, "description": "id", "example": "", "type": "integer", "schema": {"type": "integer", "format": "int64", "description": "id"}}, {"id": "anI3BF4Opz", "name": "title", "required": true, "description": "标题", "example": "", "type": "string", "schema": {"type": "string", "description": "标题", "minLength": 1}}, {"id": "qje4oxPx5b", "name": "content", "required": false, "description": "内容", "example": "", "type": "string", "schema": {"type": "string", "description": "内容"}}, {"id": "AQyWr7aRSn", "name": "cover", "required": false, "description": "封面", "example": "", "type": "integer", "schema": {"type": "integer", "format": "int64", "description": "封面"}}, {"id": "t5LtEikY69", "name": "attachment", "required": false, "description": "附件", "example": "", "type": "string", "schema": {"type": "string", "description": "附件"}}, {"id": "fYLDj2ObNq", "name": "url", "required": false, "description": "链接", "example": "", "type": "string", "schema": {"type": "string", "description": "链接"}}, {"id": "1Wkbf4pCNe", "name": "pageSize", "required": false, "description": "分页大小", "example": "", "type": "integer", "schema": {"type": "integer", "format": "int32", "description": "分页大小"}}, {"id": "QykiGrhaB8", "name": "pageNum", "required": false, "description": "当前页数", "example": "", "type": "integer", "schema": {"type": "integer", "format": "int32", "description": "当前页数"}}, {"id": "vexHVnY5Il", "name": "orderByColumn", "required": false, "description": "排序列", "example": "", "type": "string", "schema": {"type": "string", "description": "排序列"}}, {"id": "xut1BqA0r5", "name": "isAsc", "required": false, "description": "排序的方向desc或者asc", "example": "", "type": "string", "schema": {"type": "string", "description": "排序的方向desc或者asc"}}], "cookie": [], "header": []}, "auth": {}, "securityScheme": {}, "commonParameters": {}, "responses": [{"id": "761901323", "code": 200, "name": "成功", "headers": [], "jsonSchema": {"$ref": "#/definitions/199174980"}, "description": "OK", "contentType": "json", "mediaType": "*/*", "oasExtensions": ""}, {"id": "761901324", "code": 401, "name": "没有权限", "headers": [], "jsonSchema": {"type": "string"}, "description": "Unauthorized", "contentType": "json", "mediaType": "*/*", "oasExtensions": ""}], "responseExamples": [], "requestBody": {"type": "none", "parameters": [], "oasExtensions": ""}, "description": "查询政策列表", "tags": ["政策"], "status": "released", "serverId": "", "operationId": "list_1", "sourceUrl": "", "ordering": 24, "cases": [], "mocks": [], "customApiFields": "{}", "advancedSettings": {"disabledSystemHeaders": {}}, "mockScript": {}, "codeSamples": [], "commonResponseStatus": {}, "responseChildren": [], "visibility": "INHERITED", "moduleId": 6116404, "oasExtensions": "", "type": "http", "preProcessors": [], "postProcessors": [], "inheritPostProcessors": {}, "inheritPreProcessors": {}}}, {"name": "删除政策", "api": {"id": "346007954", "method": "delete", "path": "/biz/policy/{ids}", "parameters": {"path": [{"id": "ids#0", "name": "ids", "required": true, "description": "主键串", "example": "", "type": "array", "schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}], "query": [], "cookie": [], "header": []}, "auth": {}, "securityScheme": {}, "commonParameters": {}, "responses": [{"id": "761901325", "code": 200, "name": "成功", "headers": [], "jsonSchema": {"$ref": "#/definitions/*********"}, "description": "OK", "contentType": "json", "mediaType": "*/*", "oasExtensions": ""}, {"id": "761901326", "code": 401, "name": "没有权限", "headers": [], "jsonSchema": {"type": "string"}, "description": "Unauthorized", "contentType": "json", "mediaType": "*/*", "oasExtensions": ""}], "responseExamples": [], "requestBody": {"type": "none", "parameters": [], "oasExtensions": ""}, "description": "删除政策", "tags": ["政策"], "status": "released", "serverId": "", "operationId": "remove_1", "sourceUrl": "", "ordering": 30, "cases": [], "mocks": [], "customApiFields": "{}", "advancedSettings": {"disabledSystemHeaders": {}}, "mockScript": {}, "codeSamples": [], "commonResponseStatus": {}, "responseChildren": [], "visibility": "INHERITED", "moduleId": 6116404, "oasExtensions": "", "type": "http", "preProcessors": [], "postProcessors": [], "inheritPostProcessors": {}, "inheritPreProcessors": {}}}]}, {"name": "产业成果", "id": 65847311, "auth": {}, "securityScheme": {}, "parentId": 65684602, "serverId": "", "description": "", "identityPattern": {"httpApi": {"type": "inherit", "bodyType": "", "fields": []}}, "shareSettings": {}, "visibility": "INHERITED", "moduleId": 6116404, "preProcessors": [{"id": "inheritProcessors", "type": "inheritProcessors", "data": {}}], "postProcessors": [{"id": "inheritProcessors", "type": "inheritProcessors", "data": {}}], "inheritPostProcessors": {}, "inheritPreProcessors": {}, "items": [{"name": "修改产业成果", "api": {"id": "346007955", "method": "put", "path": "/biz/industryAchievement", "parameters": {"path": [], "query": [], "cookie": [], "header": []}, "auth": {}, "securityScheme": {}, "commonParameters": {}, "responses": [{"id": "761901327", "code": 200, "name": "成功", "headers": [], "jsonSchema": {"$ref": "#/definitions/*********"}, "description": "OK", "contentType": "json", "mediaType": "*/*", "oasExtensions": ""}, {"id": "761901328", "code": 401, "name": "没有权限", "headers": [], "jsonSchema": {"type": "string"}, "description": "Unauthorized", "contentType": "json", "mediaType": "*/*", "oasExtensions": ""}], "responseExamples": [], "requestBody": {"type": "application/json", "parameters": [], "jsonSchema": {"$ref": "#/definitions/199174972"}, "oasExtensions": ""}, "description": "修改产业成果", "tags": ["产业成果"], "status": "released", "serverId": "", "operationId": "edit_2", "sourceUrl": "", "ordering": 0, "cases": [], "mocks": [], "customApiFields": "{}", "advancedSettings": {"disabledSystemHeaders": {}}, "mockScript": {}, "codeSamples": [], "commonResponseStatus": {}, "responseChildren": [], "visibility": "INHERITED", "moduleId": 6116404, "oasExtensions": "", "type": "http", "preProcessors": [], "postProcessors": [], "inheritPostProcessors": {}, "inheritPreProcessors": {}}}, {"name": "新增产业成果", "api": {"id": "346007956", "method": "post", "path": "/biz/industryAchievement", "parameters": {"path": [], "query": [], "cookie": [], "header": []}, "auth": {}, "securityScheme": {}, "commonParameters": {}, "responses": [{"id": "761901329", "code": 200, "name": "成功", "headers": [], "jsonSchema": {"$ref": "#/definitions/*********"}, "description": "OK", "contentType": "json", "mediaType": "*/*", "oasExtensions": ""}, {"id": "761901330", "code": 401, "name": "没有权限", "headers": [], "jsonSchema": {"type": "string"}, "description": "Unauthorized", "contentType": "json", "mediaType": "*/*", "oasExtensions": ""}], "responseExamples": [], "requestBody": {"type": "application/json", "parameters": [], "jsonSchema": {"$ref": "#/definitions/199174972"}, "oasExtensions": ""}, "description": "新增产业成果", "tags": ["产业成果"], "status": "released", "serverId": "", "operationId": "add_2", "sourceUrl": "", "ordering": 6, "cases": [], "mocks": [], "customApiFields": "{}", "advancedSettings": {"disabledSystemHeaders": {}}, "mockScript": {}, "codeSamples": [], "commonResponseStatus": {}, "responseChildren": [], "visibility": "INHERITED", "moduleId": 6116404, "oasExtensions": "", "type": "http", "preProcessors": [], "postProcessors": [], "inheritPostProcessors": {}, "inheritPreProcessors": {}}}, {"name": "导出产业成果列表", "api": {"id": "346007957", "method": "post", "path": "/biz/industryAchievement/export", "parameters": {"path": [], "query": [{"id": "4CCP5r2VwH", "name": "id", "required": true, "description": "id", "example": "", "type": "integer", "schema": {"type": "integer", "format": "int64", "description": "id"}}, {"id": "tdn2NVftga", "name": "name", "required": true, "description": "名称", "example": "", "type": "string", "schema": {"type": "string", "description": "名称", "minLength": 1}}, {"id": "7b4uu4609K", "name": "category", "required": true, "description": "分类", "example": "", "type": "string", "schema": {"type": "string", "description": "分类", "minLength": 1}}, {"id": "g9Gby229q9", "name": "content", "required": false, "description": "内容", "example": "", "type": "string", "schema": {"type": "string", "description": "内容"}}, {"id": "Y1I1QYb1Ax", "name": "attachment", "required": true, "description": "附件", "example": "", "type": "string", "schema": {"type": "string", "description": "附件", "minLength": 1}}, {"id": "87UAJkjYJL", "name": "companyName", "required": true, "description": "公司名称", "example": "", "type": "string", "schema": {"type": "string", "description": "公司名称", "minLength": 1}}], "cookie": [], "header": []}, "auth": {}, "securityScheme": {}, "commonParameters": {}, "responses": [{"id": "*********", "code": 200, "name": "成功", "headers": [], "jsonSchema": {"type": "object", "properties": {}}, "description": "OK", "contentType": "noContent", "mediaType": "", "oasExtensions": ""}, {"id": "*********", "code": 401, "name": "没有权限", "headers": [], "jsonSchema": {"type": "object", "properties": {}}, "description": "Unauthorized", "contentType": "noContent", "mediaType": "", "oasExtensions": ""}], "responseExamples": [], "requestBody": {"type": "none", "parameters": [], "oasExtensions": ""}, "description": "导出产业成果列表", "tags": ["产业成果"], "status": "released", "serverId": "", "operationId": "export_2", "sourceUrl": "", "ordering": 12, "cases": [], "mocks": [], "customApiFields": "{}", "advancedSettings": {"disabledSystemHeaders": {}}, "mockScript": {}, "codeSamples": [], "commonResponseStatus": {}, "responseChildren": [], "visibility": "INHERITED", "moduleId": 6116404, "oasExtensions": "", "type": "http", "preProcessors": [], "postProcessors": [], "inheritPostProcessors": {}, "inheritPreProcessors": {}}}, {"name": "获取产业成果详细信息", "api": {"id": "346007958", "method": "get", "path": "/biz/industryAchievement/{id}", "parameters": {"path": [{"id": "id#0", "name": "id", "required": true, "description": "主键", "example": "", "type": "integer", "schema": {"type": "integer", "format": "int64"}}], "query": [], "cookie": [], "header": []}, "auth": {}, "securityScheme": {}, "commonParameters": {}, "responses": [{"id": "761902083", "code": 200, "name": "成功", "headers": [], "jsonSchema": {"$ref": "#/definitions/199174982"}, "description": "OK", "contentType": "json", "mediaType": "*/*", "oasExtensions": ""}, {"id": "761902084", "code": 401, "name": "没有权限", "headers": [], "jsonSchema": {"type": "string"}, "description": "Unauthorized", "contentType": "json", "mediaType": "*/*", "oasExtensions": ""}], "responseExamples": [], "requestBody": {"type": "none", "parameters": [], "oasExtensions": ""}, "description": "获取产业成果详细信息", "tags": ["产业成果"], "status": "released", "serverId": "", "operationId": "getInfo_2", "sourceUrl": "", "ordering": 18, "cases": [], "mocks": [], "customApiFields": "{}", "advancedSettings": {"disabledSystemHeaders": {}}, "mockScript": {}, "codeSamples": [], "commonResponseStatus": {}, "responseChildren": [], "visibility": "INHERITED", "moduleId": 6116404, "oasExtensions": "", "type": "http", "preProcessors": [], "postProcessors": [], "inheritPostProcessors": {}, "inheritPreProcessors": {}}}, {"name": "查询产业成果列表", "api": {"id": "346007959", "method": "get", "path": "/biz/industryAchievement/list", "parameters": {"path": [], "query": [{"id": "N147ej0Zlg", "name": "id", "required": true, "description": "id", "example": "", "type": "integer", "schema": {"type": "integer", "format": "int64", "description": "id"}}, {"id": "1LVsaOeYIH", "name": "name", "required": true, "description": "名称", "example": "", "type": "string", "schema": {"type": "string", "description": "名称", "minLength": 1}}, {"id": "mgpaYtgjzv", "name": "category", "required": true, "description": "分类", "example": "", "type": "string", "schema": {"type": "string", "description": "分类", "minLength": 1}}, {"id": "2yIp4r7HqF", "name": "content", "required": false, "description": "内容", "example": "", "type": "string", "schema": {"type": "string", "description": "内容"}}, {"id": "FNPVDBYx5i", "name": "attachment", "required": true, "description": "附件", "example": "", "type": "string", "schema": {"type": "string", "description": "附件", "minLength": 1}}, {"id": "acPVSgUJxY", "name": "companyName", "required": true, "description": "公司名称", "example": "", "type": "string", "schema": {"type": "string", "description": "公司名称", "minLength": 1}}, {"id": "Lot2fP3YC1", "name": "pageSize", "required": false, "description": "分页大小", "example": "", "type": "integer", "schema": {"type": "integer", "format": "int32", "description": "分页大小"}}, {"id": "2RPma7wgj1", "name": "pageNum", "required": false, "description": "当前页数", "example": "", "type": "integer", "schema": {"type": "integer", "format": "int32", "description": "当前页数"}}, {"id": "xY2Z3fubil", "name": "orderByColumn", "required": false, "description": "排序列", "example": "", "type": "string", "schema": {"type": "string", "description": "排序列"}}, {"id": "KR002ALfhM", "name": "isAsc", "required": false, "description": "排序的方向desc或者asc", "example": "", "type": "string", "schema": {"type": "string", "description": "排序的方向desc或者asc"}}], "cookie": [], "header": []}, "auth": {}, "securityScheme": {}, "commonParameters": {}, "responses": [{"id": "761902085", "code": 200, "name": "成功", "headers": [], "jsonSchema": {"$ref": "#/definitions/199174983"}, "description": "OK", "contentType": "json", "mediaType": "*/*", "oasExtensions": ""}, {"id": "761902086", "code": 401, "name": "没有权限", "headers": [], "jsonSchema": {"type": "string"}, "description": "Unauthorized", "contentType": "json", "mediaType": "*/*", "oasExtensions": ""}], "responseExamples": [], "requestBody": {"type": "none", "parameters": [], "oasExtensions": ""}, "description": "查询产业成果列表", "tags": ["产业成果"], "status": "released", "serverId": "", "operationId": "list_2", "sourceUrl": "", "ordering": 24, "cases": [], "mocks": [], "customApiFields": "{}", "advancedSettings": {"disabledSystemHeaders": {}}, "mockScript": {}, "codeSamples": [], "commonResponseStatus": {}, "responseChildren": [], "visibility": "INHERITED", "moduleId": 6116404, "oasExtensions": "", "type": "http", "preProcessors": [], "postProcessors": [], "inheritPostProcessors": {}, "inheritPreProcessors": {}}}, {"name": "删除产业成果", "api": {"id": "346007960", "method": "delete", "path": "/biz/industryAchievement/{ids}", "parameters": {"path": [{"id": "ids#0", "name": "ids", "required": true, "description": "主键串", "example": "", "type": "array", "schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}], "query": [], "cookie": [], "header": []}, "auth": {}, "securityScheme": {}, "commonParameters": {}, "responses": [{"id": "761902087", "code": 200, "name": "成功", "headers": [], "jsonSchema": {"$ref": "#/definitions/*********"}, "description": "OK", "contentType": "json", "mediaType": "*/*", "oasExtensions": ""}, {"id": "761902088", "code": 401, "name": "没有权限", "headers": [], "jsonSchema": {"type": "string"}, "description": "Unauthorized", "contentType": "json", "mediaType": "*/*", "oasExtensions": ""}], "responseExamples": [], "requestBody": {"type": "none", "parameters": [], "oasExtensions": ""}, "description": "删除产业成果", "tags": ["产业成果"], "status": "released", "serverId": "", "operationId": "remove_2", "sourceUrl": "", "ordering": 30, "cases": [], "mocks": [], "customApiFields": "{}", "advancedSettings": {"disabledSystemHeaders": {}}, "mockScript": {}, "codeSamples": [], "commonResponseStatus": {}, "responseChildren": [], "visibility": "INHERITED", "moduleId": 6116404, "oasExtensions": "", "type": "http", "preProcessors": [], "postProcessors": [], "inheritPostProcessors": {}, "inheritPreProcessors": {}}}]}, {"name": "企业", "id": 65847312, "auth": {}, "securityScheme": {}, "parentId": 65684602, "serverId": "", "description": "", "identityPattern": {"httpApi": {"type": "inherit", "bodyType": "", "fields": []}}, "shareSettings": {}, "visibility": "INHERITED", "moduleId": 6116404, "preProcessors": [{"id": "inheritProcessors", "type": "inheritProcessors", "data": {}}], "postProcessors": [{"id": "inheritProcessors", "type": "inheritProcessors", "data": {}}], "inheritPostProcessors": {}, "inheritPreProcessors": {}, "items": [{"name": "修改企业", "api": {"id": "*********", "method": "put", "path": "/biz/company", "parameters": {"path": [], "query": [], "cookie": [], "header": []}, "auth": {}, "securityScheme": {}, "commonParameters": {}, "responses": [{"id": "*********", "code": 200, "name": "成功", "headers": [], "jsonSchema": {"$ref": "#/definitions/*********"}, "description": "OK", "contentType": "json", "mediaType": "*/*", "oasExtensions": ""}, {"id": "*********", "code": 401, "name": "没有权限", "headers": [], "jsonSchema": {"type": "string"}, "description": "Unauthorized", "contentType": "json", "mediaType": "*/*", "oasExtensions": ""}], "responseExamples": [], "requestBody": {"type": "application/json", "parameters": [], "jsonSchema": {"$ref": "#/definitions/*********"}, "oasExtensions": ""}, "description": "修改企业", "tags": ["企业"], "status": "released", "serverId": "", "operationId": "edit_3", "sourceUrl": "", "ordering": 0, "cases": [], "mocks": [], "customApiFields": "{}", "advancedSettings": {"disabledSystemHeaders": {}}, "mockScript": {}, "codeSamples": [], "commonResponseStatus": {}, "responseChildren": [], "visibility": "INHERITED", "moduleId": 6116404, "oasExtensions": "", "type": "http", "preProcessors": [], "postProcessors": [], "inheritPostProcessors": {}, "inheritPreProcessors": {}}}, {"name": "新增企业", "api": {"id": "*********", "method": "post", "path": "/biz/company", "parameters": {"path": [], "query": [], "cookie": [], "header": []}, "auth": {}, "securityScheme": {}, "commonParameters": {}, "responses": [{"id": "*********", "code": 200, "name": "成功", "headers": [], "jsonSchema": {"$ref": "#/definitions/*********"}, "description": "OK", "contentType": "json", "mediaType": "*/*", "oasExtensions": ""}, {"id": "*********", "code": 401, "name": "没有权限", "headers": [], "jsonSchema": {"type": "string"}, "description": "Unauthorized", "contentType": "json", "mediaType": "*/*", "oasExtensions": ""}], "responseExamples": [], "requestBody": {"type": "application/json", "parameters": [], "jsonSchema": {"$ref": "#/definitions/*********"}, "oasExtensions": ""}, "description": "新增企业", "tags": ["企业"], "status": "released", "serverId": "", "operationId": "add_3", "sourceUrl": "", "ordering": 6, "cases": [], "mocks": [], "customApiFields": "{}", "advancedSettings": {"disabledSystemHeaders": {}}, "mockScript": {}, "codeSamples": [], "commonResponseStatus": {}, "responseChildren": [], "visibility": "INHERITED", "moduleId": 6116404, "oasExtensions": "", "type": "http", "preProcessors": [], "postProcessors": [], "inheritPostProcessors": {}, "inheritPreProcessors": {}}}, {"name": "导出企业列表", "api": {"id": "*********", "method": "post", "path": "/biz/company/export", "parameters": {"path": [], "query": [{"id": "CaD3BeNyyD", "name": "id", "required": true, "description": "id", "example": "", "type": "integer", "schema": {"type": "integer", "format": "int64", "description": "id"}}, {"id": "vSexouaTnX", "name": "name", "required": true, "description": "名称", "example": "", "type": "string", "schema": {"type": "string", "description": "名称", "minLength": 1}}, {"id": "x274lxPL6s", "name": "area", "required": false, "description": "所在地", "example": "", "type": "string", "schema": {"type": "string", "description": "所在地"}}, {"id": "ASXwMPH96t", "name": "contact", "required": false, "description": "联系电话", "example": "", "type": "string", "schema": {"type": "string", "description": "联系电话"}}, {"id": "c0xzsh3K5x", "name": "email", "required": false, "description": "邮箱", "example": "", "type": "string", "schema": {"type": "string", "description": "邮箱"}}, {"id": "iJzdcheLIM", "name": "logo", "required": true, "description": "logo", "example": "", "type": "integer", "schema": {"type": "integer", "format": "int64", "description": "logo"}}, {"id": "8jsWZy85lf", "name": "mainBusiness", "required": false, "description": "主营业务", "example": "", "type": "string", "schema": {"type": "string", "description": "主营业务"}}, {"id": "YdcovJRxRP", "name": "description", "required": false, "description": "简介", "example": "", "type": "string", "schema": {"type": "string", "description": "简介"}}, {"id": "iBnVHyFnUi", "name": "legalRepresentative", "required": false, "description": "法人代表", "example": "", "type": "string", "schema": {"type": "string", "description": "法人代表"}}, {"id": "Ayp9fOcnNa", "name": "registeredCapital", "required": false, "description": "注册资本", "example": "", "type": "number", "schema": {"type": "number", "format": "double", "description": "注册资本"}}, {"id": "V2nPhJb4T8", "name": "establishmentTime", "required": false, "description": "成立时间", "example": "", "type": "string", "schema": {"type": "string", "format": "date-time", "description": "成立时间"}}, {"id": "MImS6LXr7d", "name": "creditCode", "required": false, "description": "信用代码", "example": "", "type": "string", "schema": {"type": "string", "description": "信用代码"}}, {"id": "ETtxFOg5tR", "name": "industry", "required": false, "description": "行业", "example": "", "type": "string", "schema": {"type": "string", "description": "行业"}}, {"id": "gcBHh3YcNB", "name": "financingList", "required": false, "description": "融资信息", "example": "", "type": "array", "schema": {"type": "array", "description": "融资信息", "items": {"$ref": "#/definitions/*********"}}}], "cookie": [], "header": []}, "auth": {}, "securityScheme": {}, "commonParameters": {}, "responses": [{"id": "761902093", "code": 200, "name": "成功", "headers": [], "jsonSchema": {"type": "object", "properties": {}}, "description": "OK", "contentType": "noContent", "mediaType": "", "oasExtensions": ""}, {"id": "761902094", "code": 401, "name": "没有权限", "headers": [], "jsonSchema": {"type": "object", "properties": {}}, "description": "Unauthorized", "contentType": "noContent", "mediaType": "", "oasExtensions": ""}], "responseExamples": [], "requestBody": {"type": "none", "parameters": [], "oasExtensions": ""}, "description": "导出企业列表", "tags": ["企业"], "status": "released", "serverId": "", "operationId": "export_3", "sourceUrl": "", "ordering": 12, "cases": [], "mocks": [], "customApiFields": "{}", "advancedSettings": {"disabledSystemHeaders": {}}, "mockScript": {}, "codeSamples": [], "commonResponseStatus": {}, "responseChildren": [], "visibility": "INHERITED", "moduleId": 6116404, "oasExtensions": "", "type": "http", "preProcessors": [], "postProcessors": [], "inheritPostProcessors": {}, "inheritPreProcessors": {}}}, {"name": "获取企业详细信息", "api": {"id": "*********", "method": "get", "path": "/biz/company/{id}", "parameters": {"path": [{"id": "id#0", "name": "id", "required": true, "description": "主键", "example": "", "type": "integer", "schema": {"type": "integer", "format": "int64"}}], "query": [], "cookie": [], "header": []}, "auth": {}, "securityScheme": {}, "commonParameters": {}, "responses": [{"id": "*********", "code": 200, "name": "成功", "headers": [], "jsonSchema": {"$ref": "#/definitions/*********"}, "description": "OK", "contentType": "json", "mediaType": "*/*", "oasExtensions": ""}, {"id": "*********", "code": 401, "name": "没有权限", "headers": [], "jsonSchema": {"type": "string"}, "description": "Unauthorized", "contentType": "json", "mediaType": "*/*", "oasExtensions": ""}], "responseExamples": [], "requestBody": {"type": "none", "parameters": [], "oasExtensions": ""}, "description": "获取企业详细信息", "tags": ["企业"], "status": "released", "serverId": "", "operationId": "getInfo_3", "sourceUrl": "", "ordering": 18, "cases": [], "mocks": [], "customApiFields": "{}", "advancedSettings": {"disabledSystemHeaders": {}}, "mockScript": {}, "codeSamples": [], "commonResponseStatus": {}, "responseChildren": [], "visibility": "INHERITED", "moduleId": 6116404, "oasExtensions": "", "type": "http", "preProcessors": [], "postProcessors": [], "inheritPostProcessors": {}, "inheritPreProcessors": {}}}, {"name": "查询企业列表", "api": {"id": "*********", "method": "get", "path": "/biz/company/list", "parameters": {"path": [], "query": [{"id": "XMY4bajICG", "name": "id", "required": true, "description": "id", "example": "", "type": "integer", "schema": {"type": "integer", "format": "int64", "description": "id"}}, {"id": "0LjUy11TCz", "name": "name", "required": true, "description": "名称", "example": "", "type": "string", "schema": {"type": "string", "description": "名称", "minLength": 1}}, {"id": "0yHCAVBppX", "name": "area", "required": false, "description": "所在地", "example": "", "type": "string", "schema": {"type": "string", "description": "所在地"}}, {"id": "UemWwXjTvB", "name": "contact", "required": false, "description": "联系电话", "example": "", "type": "string", "schema": {"type": "string", "description": "联系电话"}}, {"id": "yKvJGt1ld1", "name": "email", "required": false, "description": "邮箱", "example": "", "type": "string", "schema": {"type": "string", "description": "邮箱"}}, {"id": "WcRL9qJPYm", "name": "logo", "required": true, "description": "logo", "example": "", "type": "integer", "schema": {"type": "integer", "format": "int64", "description": "logo"}}, {"id": "v8P9wxCj32", "name": "mainBusiness", "required": false, "description": "主营业务", "example": "", "type": "string", "schema": {"type": "string", "description": "主营业务"}}, {"id": "N8weT222vb", "name": "description", "required": false, "description": "简介", "example": "", "type": "string", "schema": {"type": "string", "description": "简介"}}, {"id": "m8ZC3sD1cx", "name": "legalRepresentative", "required": false, "description": "法人代表", "example": "", "type": "string", "schema": {"type": "string", "description": "法人代表"}}, {"id": "EQi2C46cFY", "name": "registeredCapital", "required": false, "description": "注册资本", "example": "", "type": "number", "schema": {"type": "number", "format": "double", "description": "注册资本"}}, {"id": "zzEyoK5h64", "name": "establishmentTime", "required": false, "description": "成立时间", "example": "", "type": "string", "schema": {"type": "string", "format": "date-time", "description": "成立时间"}}, {"id": "CtuFS7KGdK", "name": "creditCode", "required": false, "description": "信用代码", "example": "", "type": "string", "schema": {"type": "string", "description": "信用代码"}}, {"id": "KpAvDr42ur", "name": "industry", "required": false, "description": "行业", "example": "", "type": "string", "schema": {"type": "string", "description": "行业"}}, {"id": "j7PcJcwYhK", "name": "financingList", "required": false, "description": "融资信息", "example": "", "type": "array", "schema": {"type": "array", "description": "融资信息", "items": {"$ref": "#/definitions/*********"}}}, {"id": "ozgWwPjsQg", "name": "pageSize", "required": false, "description": "分页大小", "example": "", "type": "integer", "schema": {"type": "integer", "format": "int32", "description": "分页大小"}}, {"id": "1xfijz4BAx", "name": "pageNum", "required": false, "description": "当前页数", "example": "", "type": "integer", "schema": {"type": "integer", "format": "int32", "description": "当前页数"}}, {"id": "96C9ATUETF", "name": "orderByColumn", "required": false, "description": "排序列", "example": "", "type": "string", "schema": {"type": "string", "description": "排序列"}}, {"id": "JLBXobw2BY", "name": "isAsc", "required": false, "description": "排序的方向desc或者asc", "example": "", "type": "string", "schema": {"type": "string", "description": "排序的方向desc或者asc"}}], "cookie": [], "header": []}, "auth": {}, "securityScheme": {}, "commonParameters": {}, "responses": [{"id": "761902097", "code": 200, "name": "成功", "headers": [], "jsonSchema": {"$ref": "#/definitions/*********"}, "description": "OK", "contentType": "json", "mediaType": "*/*", "oasExtensions": ""}, {"id": "761902098", "code": 401, "name": "没有权限", "headers": [], "jsonSchema": {"type": "string"}, "description": "Unauthorized", "contentType": "json", "mediaType": "*/*", "oasExtensions": ""}], "responseExamples": [], "requestBody": {"type": "none", "parameters": [], "oasExtensions": ""}, "description": "查询企业列表", "tags": ["企业"], "status": "released", "serverId": "", "operationId": "list_3", "sourceUrl": "", "ordering": 24, "cases": [], "mocks": [], "customApiFields": "{}", "advancedSettings": {"disabledSystemHeaders": {}}, "mockScript": {}, "codeSamples": [], "commonResponseStatus": {}, "responseChildren": [], "visibility": "INHERITED", "moduleId": 6116404, "oasExtensions": "", "type": "http", "preProcessors": [], "postProcessors": [], "inheritPostProcessors": {}, "inheritPreProcessors": {}}}, {"name": "删除企业", "api": {"id": "*********", "method": "delete", "path": "/biz/company/{ids}", "parameters": {"path": [{"id": "ids#0", "name": "ids", "required": true, "description": "主键串", "example": "", "type": "array", "schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}], "query": [], "cookie": [], "header": []}, "auth": {}, "securityScheme": {}, "commonParameters": {}, "responses": [{"id": "*********", "code": 200, "name": "成功", "headers": [], "jsonSchema": {"$ref": "#/definitions/*********"}, "description": "OK", "contentType": "json", "mediaType": "*/*", "oasExtensions": ""}, {"id": "*********", "code": 401, "name": "没有权限", "headers": [], "jsonSchema": {"type": "string"}, "description": "Unauthorized", "contentType": "json", "mediaType": "*/*", "oasExtensions": ""}], "responseExamples": [], "requestBody": {"type": "none", "parameters": [], "oasExtensions": ""}, "description": "删除企业", "tags": ["企业"], "status": "released", "serverId": "", "operationId": "remove_3", "sourceUrl": "", "ordering": 30, "cases": [], "mocks": [], "customApiFields": "{}", "advancedSettings": {"disabledSystemHeaders": {}}, "mockScript": {}, "codeSamples": [], "commonResponseStatus": {}, "responseChildren": [], "visibility": "INHERITED", "moduleId": 6116404, "oasExtensions": "", "type": "http", "preProcessors": [], "postProcessors": [], "inheritPostProcessors": {}, "inheritPreProcessors": {}}}]}]}]}], "socketCollection": [], "docCollection": [], "webSocketCollection": [], "socketIOCollection": [], "responseCollection": [], "schemaCollection": [{"id": 16231281, "name": "根目录", "visibility": "SHARED", "moduleId": 6116404, "items": [{"id": 16231388, "name": "<PERSON><PERSON><PERSON>", "visibility": "INHERITED", "moduleId": 6116404, "items": [{"name": "BizThirdPartyOrgBo", "displayName": "", "id": "#/definitions/199174970", "description": "第三方机构业务对象 biz_third_party_org", "schema": {"jsonSchema": {"type": "object", "description": "第三方机构业务对象 biz_third_party_org", "properties": {"id": {"type": "integer", "format": "int64", "description": "id"}, "name": {"type": "string", "description": "名称", "minLength": 1}, "logo": {"type": "integer", "format": "int64", "description": "logo"}, "category": {"type": "string", "description": "分类", "minLength": 1}, "content": {"type": "string", "description": "内容"}, "serviceScope": {"type": "string", "description": "服务范围"}, "attachment": {"type": "string", "description": "附件"}}, "required": ["category", "id", "name"], "x-apifox-orders": ["id", "name", "logo", "category", "content", "serviceScope", "attachment"]}}, "visibility": "INHERITED", "moduleId": 6116404}, {"name": "RVoid", "displayName": "", "id": "#/definitions/*********", "description": "响应信息主体", "schema": {"jsonSchema": {"type": "object", "description": "响应信息主体", "properties": {"code": {"type": "integer", "format": "int32"}, "msg": {"type": "string"}, "data": {}}, "x-apifox-orders": ["code", "msg", "data"]}}, "visibility": "INHERITED", "moduleId": 6116404}, {"name": "BizPolicyBo", "displayName": "", "id": "#/definitions/199174971", "description": "政策业务对象 biz_policy", "schema": {"jsonSchema": {"type": "object", "description": "政策业务对象 biz_policy", "properties": {"id": {"type": "integer", "format": "int64", "description": "id"}, "title": {"type": "string", "description": "标题", "minLength": 1}, "content": {"type": "string", "description": "内容"}, "cover": {"type": "integer", "format": "int64", "description": "封面"}, "attachment": {"type": "string", "description": "附件"}, "url": {"type": "string", "description": "链接"}}, "required": ["id", "title"], "x-apifox-orders": ["id", "title", "content", "cover", "attachment", "url"]}}, "visibility": "INHERITED", "moduleId": 6116404}, {"name": "BizIndustryAchievementBo", "displayName": "", "id": "#/definitions/199174972", "description": "产业成果业务对象 biz_industry_achievement", "schema": {"jsonSchema": {"type": "object", "description": "产业成果业务对象 biz_industry_achievement", "properties": {"id": {"type": "integer", "format": "int64", "description": "id"}, "name": {"type": "string", "description": "名称", "minLength": 1}, "category": {"type": "string", "description": "分类", "minLength": 1}, "content": {"type": "string", "description": "内容"}, "attachment": {"type": "string", "description": "附件", "minLength": 1}, "companyName": {"type": "string", "description": "公司名称", "minLength": 1}}, "required": ["attachment", "category", "companyName", "id", "name"], "x-apifox-orders": ["id", "name", "category", "content", "attachment", "companyName"]}}, "visibility": "INHERITED", "moduleId": 6116404}, {"name": "BizCompanyBo", "displayName": "", "id": "#/definitions/*********", "description": "企业业务对象 biz_company", "schema": {"jsonSchema": {"type": "object", "description": "企业业务对象 biz_company", "properties": {"id": {"type": "integer", "format": "int64", "description": "id"}, "name": {"type": "string", "description": "名称", "minLength": 1}, "area": {"type": "string", "description": "所在地"}, "contact": {"type": "string", "description": "联系电话"}, "email": {"type": "string", "description": "邮箱"}, "logo": {"type": "integer", "format": "int64", "description": "logo"}, "mainBusiness": {"type": "string", "description": "主营业务"}, "description": {"type": "string", "description": "简介"}, "legalRepresentative": {"type": "string", "description": "法人代表"}, "registeredCapital": {"type": "number", "format": "double", "description": "注册资本"}, "establishmentTime": {"type": "string", "format": "date-time", "description": "成立时间"}, "creditCode": {"type": "string", "description": "信用代码"}, "industry": {"type": "string", "description": "行业"}, "financingList": {"type": "array", "description": "融资信息", "items": {"$ref": "#/definitions/*********"}}}, "required": ["id", "logo", "name"], "x-apifox-orders": ["id", "name", "area", "contact", "email", "logo", "mainBusiness", "description", "legalRepresentative", "registeredCapital", "establishmentTime", "creditCode", "industry", "financingList"]}}, "visibility": "INHERITED", "moduleId": 6116404}, {"name": "RBizThirdPartyOrgVo", "displayName": "", "id": "#/definitions/199174976", "description": "响应信息主体", "schema": {"jsonSchema": {"type": "object", "description": "响应信息主体", "properties": {"code": {"type": "integer", "format": "int32"}, "msg": {"type": "string"}, "data": {"$ref": "#/definitions/199174975"}}, "x-apifox-orders": ["code", "msg", "data"]}}, "visibility": "INHERITED", "moduleId": 6116404}, {"name": "TableDataInfoBizThirdPartyOrgVo", "displayName": "", "id": "#/definitions/199174977", "description": "表格分页数据对象", "schema": {"jsonSchema": {"type": "object", "description": "表格分页数据对象", "properties": {"total": {"type": "integer", "format": "int64", "description": "总记录数"}, "rows": {"type": "array", "description": "列表数据", "items": {"$ref": "#/definitions/199174975"}}, "code": {"type": "integer", "format": "int32", "description": "消息状态码"}, "msg": {"type": "string", "description": "消息内容"}}, "x-apifox-orders": ["total", "rows", "code", "msg"]}}, "visibility": "INHERITED", "moduleId": 6116404}, {"name": "RBizPolicyVo", "displayName": "", "id": "#/definitions/199174979", "description": "响应信息主体", "schema": {"jsonSchema": {"type": "object", "description": "响应信息主体", "properties": {"code": {"type": "integer", "format": "int32"}, "msg": {"type": "string"}, "data": {"$ref": "#/definitions/199174978"}}, "x-apifox-orders": ["code", "msg", "data"]}}, "visibility": "INHERITED", "moduleId": 6116404}, {"name": "TableDataInfoBizPolicyVo", "displayName": "", "id": "#/definitions/199174980", "description": "表格分页数据对象", "schema": {"jsonSchema": {"type": "object", "description": "表格分页数据对象", "properties": {"total": {"type": "integer", "format": "int64", "description": "总记录数"}, "rows": {"type": "array", "description": "列表数据", "items": {"$ref": "#/definitions/199174978"}}, "code": {"type": "integer", "format": "int32", "description": "消息状态码"}, "msg": {"type": "string", "description": "消息内容"}}, "x-apifox-orders": ["total", "rows", "code", "msg"]}}, "visibility": "INHERITED", "moduleId": 6116404}, {"name": "RBizIndustryAchievementVo", "displayName": "", "id": "#/definitions/199174982", "description": "响应信息主体", "schema": {"jsonSchema": {"type": "object", "description": "响应信息主体", "properties": {"code": {"type": "integer", "format": "int32"}, "msg": {"type": "string"}, "data": {"$ref": "#/definitions/*********"}}, "x-apifox-orders": ["code", "msg", "data"]}}, "visibility": "INHERITED", "moduleId": 6116404}, {"name": "TableDataInfoBizIndustryAchievementVo", "displayName": "", "id": "#/definitions/199174983", "description": "表格分页数据对象", "schema": {"jsonSchema": {"type": "object", "description": "表格分页数据对象", "properties": {"total": {"type": "integer", "format": "int64", "description": "总记录数"}, "rows": {"type": "array", "description": "列表数据", "items": {"$ref": "#/definitions/*********"}}, "code": {"type": "integer", "format": "int32", "description": "消息状态码"}, "msg": {"type": "string", "description": "消息内容"}}, "x-apifox-orders": ["total", "rows", "code", "msg"]}}, "visibility": "INHERITED", "moduleId": 6116404}, {"name": "RBizCompanyVo", "displayName": "", "id": "#/definitions/*********", "description": "响应信息主体", "schema": {"jsonSchema": {"type": "object", "description": "响应信息主体", "properties": {"code": {"type": "integer", "format": "int32"}, "msg": {"type": "string"}, "data": {"$ref": "#/definitions/*********"}}, "x-apifox-orders": ["code", "msg", "data"]}}, "visibility": "INHERITED", "moduleId": 6116404}, {"name": "TableDataInfoBizCompanyVo", "displayName": "", "id": "#/definitions/*********", "description": "表格分页数据对象", "schema": {"jsonSchema": {"type": "object", "description": "表格分页数据对象", "properties": {"total": {"type": "integer", "format": "int64", "description": "总记录数"}, "rows": {"type": "array", "description": "列表数据", "items": {"$ref": "#/definitions/*********"}}, "code": {"type": "integer", "format": "int32", "description": "消息状态码"}, "msg": {"type": "string", "description": "消息内容"}}, "x-apifox-orders": ["total", "rows", "code", "msg"]}}, "visibility": "INHERITED", "moduleId": 6116404}, {"name": "BizFinancingBo", "displayName": "", "id": "#/definitions/*********", "description": "融资信息业务对象 biz_financing", "schema": {"jsonSchema": {"type": "object", "description": "融资信息业务对象 biz_financing", "properties": {"fundingTime": {"type": "string", "format": "date-time", "description": "融资时间"}, "fundingStage": {"type": "string", "description": "融资阶段"}, "fundingAmount": {"type": "number", "format": "double", "description": "融资金额(万元)"}, "investor": {"type": "string", "description": "投资方"}}, "x-apifox-orders": ["fundingTime", "fundingStage", "fundingAmount", "investor"]}}, "visibility": "INHERITED", "moduleId": 6116404}, {"name": "BizThirdPartyOrgVo", "displayName": "", "id": "#/definitions/199174975", "description": "第三方机构视图对象 biz_third_party_org", "schema": {"jsonSchema": {"type": "object", "description": "第三方机构视图对象 biz_third_party_org", "properties": {"id": {"type": "integer", "format": "int64", "description": "id"}, "name": {"type": "string", "description": "名称"}, "logo": {"type": "integer", "format": "int64", "description": "logo"}, "logoUrl": {"type": "string", "description": "logo URL"}, "category": {"type": "string", "description": "分类"}, "content": {"type": "string", "description": "内容"}, "serviceScope": {"type": "string", "description": "服务范围"}, "attachment": {"type": "string", "description": "附件"}, "attachmentUrl": {"type": "string", "description": "附件URL"}, "createTime": {"type": "string", "format": "date-time", "description": "创建时间"}, "createBy": {"type": "string", "description": "创建人"}}, "x-apifox-orders": ["id", "name", "logo", "logoUrl", "category", "content", "serviceScope", "attachment", "attachmentUrl", "createTime", "createBy"]}}, "visibility": "INHERITED", "moduleId": 6116404}, {"name": "BizPolicyVo", "displayName": "", "id": "#/definitions/199174978", "description": "政策视图对象 biz_policy", "schema": {"jsonSchema": {"type": "object", "description": "政策视图对象 biz_policy", "properties": {"id": {"type": "integer", "format": "int64", "description": "id"}, "title": {"type": "string", "description": "标题"}, "content": {"type": "string", "description": "内容"}, "cover": {"type": "integer", "format": "int64", "description": "封面"}, "coverUrl": {"type": "integer", "format": "int64", "description": "封面"}, "attachment": {"type": "string", "description": "附件"}, "attachmentUrl": {"type": "string", "description": "附件"}, "url": {"type": "string", "description": "链接"}, "createTime": {"type": "string", "format": "date-time", "description": "创建时间"}, "createBy": {"type": "string", "description": "创建人"}}, "x-apifox-orders": ["id", "title", "content", "cover", "coverUrl", "attachment", "attachmentUrl", "url", "createTime", "createBy"]}}, "visibility": "INHERITED", "moduleId": 6116404}, {"name": "BizIndustryAchievementVo", "displayName": "", "id": "#/definitions/*********", "description": "产业成果视图对象 biz_industry_achievement", "schema": {"jsonSchema": {"type": "object", "description": "产业成果视图对象 biz_industry_achievement", "properties": {"id": {"type": "integer", "format": "int64", "description": "id"}, "name": {"type": "string", "description": "名称"}, "category": {"type": "string", "description": "分类"}, "content": {"type": "string", "description": "内容"}, "attachment": {"type": "string", "description": "附件"}, "attachmentUrl": {"type": "string", "description": "附件URL"}, "companyName": {"type": "string", "description": "公司名称"}, "createTime": {"type": "string", "format": "date-time", "description": "创建时间"}, "createBy": {"type": "string", "description": "创建人"}}, "x-apifox-orders": ["id", "name", "category", "content", "attachment", "attachmentUrl", "companyName", "createTime", "createBy"]}}, "visibility": "INHERITED", "moduleId": 6116404}, {"name": "BizCompanyVo", "displayName": "", "id": "#/definitions/*********", "description": "企业视图对象 biz_company", "schema": {"jsonSchema": {"type": "object", "description": "企业视图对象 biz_company", "properties": {"id": {"type": "integer", "format": "int64", "description": "id"}, "name": {"type": "string", "description": "名称"}, "area": {"type": "string", "description": "所在地"}, "contact": {"type": "string", "description": "联系电话"}, "email": {"type": "string", "description": "邮箱"}, "logo": {"type": "integer", "format": "int64", "description": "logo"}, "logoUrl": {"type": "string", "description": "logo URL"}, "mainBusiness": {"type": "string", "description": "主营业务"}, "description": {"type": "string", "description": "简介"}, "legalRepresentative": {"type": "string", "description": "法人代表"}, "registeredCapital": {"type": "number", "format": "double", "description": "注册资本"}, "establishmentTime": {"type": "string", "format": "date-time", "description": "成立时间"}, "creditCode": {"type": "string", "description": "信用代码"}, "industry": {"type": "string", "description": "行业"}, "createTime": {"type": "string", "format": "date-time", "description": "创建时间"}, "createBy": {"type": "string", "description": "创建人"}, "financingList": {"type": "array", "description": "融资信息", "items": {"$ref": "#/definitions/199174985"}}}, "x-apifox-orders": ["id", "name", "area", "contact", "email", "logo", "logoUrl", "mainBusiness", "description", "legalRepresentative", "registeredCapital", "establishmentTime", "creditCode", "industry", "createTime", "createBy", "financingList"]}}, "visibility": "INHERITED", "moduleId": 6116404}, {"name": "BizFinancingVo", "displayName": "", "id": "#/definitions/199174985", "description": "融资信息视图对象 biz_financing", "schema": {"jsonSchema": {"type": "object", "description": "融资信息视图对象 biz_financing", "properties": {"id": {"type": "integer", "format": "int64", "description": "id"}, "fundingTime": {"type": "string", "format": "date-time", "description": "融资时间"}, "fundingStage": {"type": "string", "description": "融资阶段"}, "fundingAmount": {"type": "number", "format": "double", "description": "融资金额(万元)"}, "investor": {"type": "string", "description": "投资方"}}, "x-apifox-orders": ["id", "fundingTime", "fundingStage", "fundingAmount", "investor"]}}, "visibility": "INHERITED", "moduleId": 6116404}]}]}], "securitySchemeCollection": [], "requestCollection": [{"name": "根目录", "children": [], "ordering": ["requestFolder.7519356"], "items": []}], "environments": [], "commonScripts": [], "globalVariables": [], "commonParameters": null, "projectSetting": {"id": "7093216", "auth": {}, "securityScheme": {}, "gateway": [], "language": "zh-CN", "apiStatuses": ["developing", "testing", "released", "deprecated"], "mockSettings": {}, "preProcessors": [], "postProcessors": [], "advancedSettings": {"enableJsonc": false, "enableBigint": false, "responseValidate": true, "enableTestScenarioSetting": false, "enableYAPICompatScript": false, "isDefaultUrlEncoding": 2, "publishedDocUrlRules": {"defaultRule": "RESOURCE_KEY_ONLY", "resourceKeyStandard": "NEW"}}, "initialDisabledMockIds": [], "servers": [{"id": "default", "name": "默认服务"}], "cloudMock": {"security": "free", "enable": false, "tokenKey": "apifoxToken"}}, "customFunctions": [], "projectTestCaseCategories": [{"id": 1713962, "name": "正向"}, {"id": 1713963, "name": "负向"}, {"id": 1713964, "name": "边界值"}, {"id": 1713965, "name": "安全性"}, {"id": 1713966, "name": "其他"}], "projectAssociations": [], "moduleSettings": [{"id": "6116404", "name": "默认模块", "moduleVariables": []}]}