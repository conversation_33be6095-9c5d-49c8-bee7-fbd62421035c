/**
 * Colors
 * -------------------------------------------------------------------------- */
:root {
  --vp-c-brand-1: hsl(128, 56%, 38%);
  --vp-c-brand-2: hsl(128, 56%, 55%);
  --vp-c-brand-3: hsl(128, 56%, 45%);
  --vp-c-brand-soft: rgba(98, 133, 208, 0.16);
}
/**
   * Component: Home
   * -------------------------------------------------------------------------- */

:root {
  --vp-home-hero-name-color: transparent;
  --vp-home-hero-name-background: -webkit-linear-gradient(120deg, hsl(128, 56%, 38%) 30%, hsl(128, 56%, 60%));
  --vp-home-hero-image-background-image: linear-gradient(120deg, hsl(100, 56%, 45%) 30%, hsl(120, 56%, 38%));
  --vp-home-hero-image-filter: blur(40px);
}
@media (min-width: 640px) {
  :root {
    --vp-home-hero-image-filter: blur(56px);
  }
}

@media (min-width: 960px) {
  :root {
    --vp-home-hero-image-filter: blur(72px);
  }
}

.md-center > p {
  display: flex;
  /* justify-content: center; */
  flex-wrap: wrap;
  margin-top: -4px;
  margin-right: -4px;
}
.md-center img {
  display: inline-block;
  height: 1.4em;
  margin-top: 4px;
  margin-right: 4px;
  line-height: 1.6;
}
.md-center2 img {
  display: inline-block;
  margin-top: 4px;
  margin-right: 4px;
}

.busuanzi_container {
  display: flex;
  justify-content: space-around;
  opacity: 0.3;
}

@media (max-width: 960px) {
  .busuanzi_container {
    flex-direction: column;
    padding-top: 12px;
    padding-left: 30px;
  }
}
@media (min-width: 961px) {
  .busuanzi_container {
    height: 60px;
    line-height: 60px;
  }
}

.icp_container {
  display: flex;
  align-items: center;
  justify-content: center;
}
