<route lang="jsonc" type="page">
{
  "style": {
    "navigationBarTitleText": "机构详情"
  }
}
</route>

<script setup lang="ts">
import type { Institution } from '@/api/types/institution'
import { onLoad } from '@dcloudio/uni-app'
import { ref } from 'vue'

const institutionDetail = ref<Institution | null>(null)
const loading = ref(true)

// 模拟机构详情数据
const mockInstitutionDetails: Record<number, Institution> = {
  1: {
    id: 1,
    name: '中国银行科创金融部',
    type: '金融',
    logo: '/static/images/default-avatar.png',
    description: '专注于科技创新企业的金融服务，提供贷款、投资、咨询等一站式金融解决方案',
    services: ['科创贷款', '风险投资', '金融咨询', '上市辅导', '并购融资', '债券发行'],
    cases: [
      {
        title: '某AI科技公司A轮融资',
        description: '协助完成5000万元A轮融资，估值达到5亿元',
        result: '成功融资，公司快速发展',
      },
      {
        title: '某生物医药企业B轮融资',
        description: '主导完成2亿元B轮融资，引入战略投资者',
        result: '融资成功，研发加速推进',
      },
      {
        title: '某新能源公司上市辅导',
        description: '提供全面的上市辅导服务，帮助企业成功在科创板上市',
        result: '成功上市，市值超50亿元',
      },
    ],
    contact: {
      phone: '************',
      email: '<EMAIL>',
      address: '北京市西城区金融大街1号',
      website: 'www.boc.cn',
    },
  },
  2: {
    id: 2,
    name: '金杜律师事务所',
    type: '法律',
    logo: '/static/images/default-avatar.png',
    description: '国际知名的综合性律师事务所，为企业提供全方位的法律服务和解决方案',
    services: ['公司法律', '知识产权', '并购重组', '争议解决', '合规咨询', '跨境业务'],
    cases: [
      {
        title: '某科技公司IPO项目',
        description: '成功辅导公司在科创板上市，融资规模20亿元',
        result: '成功上市，市值超百亿',
      },
      {
        title: '某跨国并购项目',
        description: '代表中国企业完成海外并购，交易金额15亿美元',
        result: '并购成功，业务拓展至全球',
      },
      {
        title: '知识产权保护项目',
        description: '为创新企业提供全面的知识产权保护和维权服务',
        result: '成功维权，保护企业核心技术',
      },
    ],
    contact: {
      phone: '010-5878-5555',
      email: '<EMAIL>',
      address: '北京市朝阳区东大桥路9号',
      website: 'www.kingandwood.com',
    },
  },
  3: {
    id: 3,
    name: '麦肯锡咨询',
    type: '咨询',
    logo: '/static/images/default-avatar.png',
    description: '全球领先的管理咨询公司，为企业提供战略规划、运营优化等专业咨询服务',
    services: ['战略咨询', '数字化转型', '组织变革', '运营优化', '市场研究', '绩效管理'],
    cases: [
      {
        title: '某制造企业数字化转型',
        description: '帮助企业实现数字化升级，提升生产效率30%',
        result: '成功转型，效益显著提升',
      },
      {
        title: '某零售企业战略规划',
        description: '制定五年发展战略，助力企业市场份额提升20%',
        result: '战略落地，业绩持续增长',
      },
      {
        title: '某金融机构组织变革',
        description: '优化组织架构，提升运营效率25%',
        result: '变革成功，成本降低效益提升',
      },
    ],
    contact: {
      phone: '021-2308-8888',
      email: '<EMAIL>',
      address: '上海市浦东新区世纪大道8号',
      website: 'www.mckinsey.com',
    },
  },
  4: {
    id: 4,
    name: '华为云技术服务中心',
    type: '技术',
    logo: '/static/images/default-avatar.png',
    description: '提供云计算、人工智能、大数据等前沿技术服务，助力企业数字化转型',
    services: ['云计算', '人工智能', '大数据', '物联网', '区块链', '边缘计算'],
    cases: [
      {
        title: '某政府智慧城市项目',
        description: '建设智慧城市平台，提升城市管理效率50%',
        result: '项目成功落地，获得政府好评',
      },
      {
        title: '某制造企业工业互联网',
        description: '构建工业互联网平台，实现生产数据实时监控和分析',
        result: '生产效率提升35%，成本降低20%',
      },
      {
        title: '某金融机构AI风控',
        description: '部署AI风控系统，提升风险识别准确率90%',
        result: '风控效果显著提升，坏账率降低60%',
      },
    ],
    contact: {
      phone: '950808',
      email: '<EMAIL>',
      address: '广东省深圳市龙岗区坂田华为基地',
      website: 'www.huaweicloud.com',
    },
  },
}

onLoad((options) => {
  const id = Number(options?.id) || 1
  loadInstitutionDetail(id)
})

// 加载机构详情
function loadInstitutionDetail(id: number) {
  loading.value = true
  setTimeout(() => {
    institutionDetail.value = mockInstitutionDetails[id] || mockInstitutionDetails[1]
    loading.value = false
  }, 500)
}

// 返回上一页
function goBack() {
  uni.navigateBack()
}

// 拨打电话
function makePhoneCall(phone: string) {
  uni.makePhoneCall({
    phoneNumber: phone,
  })
}

// 发送邮件
function sendEmail(email: string) {
  uni.navigateTo({
    url: `mailto:${email}`,
  })
}

// 打开网站
function openWebsite(url: string) {
  uni.navigateTo({
    url: `/pages/webview/index?url=${encodeURIComponent(url)}`,
  })
}
</script>

<template>
  <view v-if="institutionDetail" class="min-h-screen bg-gray-50 pb-20">
    <!-- 返回按钮 -->
    <view class="bg-white px-4 pt-4">
      <wd-icon name="arrow-left" size="20" @click="goBack" />
    </view>

    <!-- 机构头部信息 -->
    <view class="bg-white p-4">
      <view class="mb-4 flex items-center">
        <image
          :src="institutionDetail.logo"
          class="mr-4 h-16 w-16 rounded-lg"
          mode="aspectFill"
        />
        <view class="flex-1">
          <text class="block text-xl text-gray-800 font-bold">
            {{ institutionDetail.name }}
          </text>
          <text class="mt-1 block text-sm text-blue-500">
            {{ institutionDetail.type }}服务机构
          </text>
          <text class="mt-2 block text-sm text-gray-600">
            {{ institutionDetail.description }}
          </text>
        </view>
      </view>
    </view>

    <!-- 服务范围 -->
    <view class="mt-4 bg-white p-4">
      <text class="mb-3 block text-lg font-bold">
        服务范围
      </text>
      <view class="flex flex-wrap gap-2">
        <text
          v-for="service in institutionDetail.services"
          :key="service"
          class="rounded-full bg-blue-50 px-3 py-1 text-sm text-blue-600"
        >
          {{ service }}
        </text>
      </view>
    </view>

    <!-- 成功案例 -->
    <view class="mt-4 bg-white p-4">
      <text class="mb-3 block text-lg font-bold">
        成功案例
      </text>
      <view class="space-y-3">
        <view
          v-for="(caseItem, index) in institutionDetail.cases"
          :key="index"
          class="rounded-lg bg-gray-50 p-3"
        >
          <text class="block text-gray-800 font-medium">
            {{ caseItem.title }}
          </text>
          <text class="mt-1 block text-sm text-gray-600">
            {{ caseItem.description }}
          </text>
          <text class="mt-2 block text-sm text-green-600">
            成果：{{ caseItem.result }}
          </text>
        </view>
      </view>
    </view>

    <!-- 联系方式 -->
    <view class="mt-4 bg-white p-4">
      <text class="mb-3 block text-lg font-bold">
        联系方式
      </text>
      <view class="space-y-3">
        <!-- 电话 -->
        <view class="flex items-center justify-between">
          <view class="flex items-center">
            <text class="i-carbon-phone mr-2 text-blue-500" />
            <text class="text-gray-700">
              {{ institutionDetail.contact.phone }}
            </text>
          </view>
          <wd-button
            size="small"
            type="primary"
            @click="makePhoneCall(institutionDetail.contact.phone)"
          >
            拨打
          </wd-button>
        </view>

        <!-- 邮箱 -->
        <view class="flex items-center justify-between">
          <view class="flex items-center">
            <text class="i-carbon-email mr-2 text-blue-500" />
            <text class="text-gray-700">
              {{ institutionDetail.contact.email }}
            </text>
          </view>
          <wd-button
            size="small"
            type="default"
            @click="sendEmail(institutionDetail.contact.email)"
          >
            邮件
          </wd-button>
        </view>

        <!-- 地址 -->
        <view v-if="institutionDetail.contact.address" class="flex items-center">
          <text class="i-carbon-location mr-2 text-blue-500" />
          <text class="flex-1 text-gray-700">
            {{ institutionDetail.contact.address }}
          </text>
        </view>

        <!-- 网站 -->
        <view v-if="institutionDetail.contact.website" class="flex items-center justify-between">
          <view class="flex items-center">
            <text class="i-carbon-link mr-2 text-blue-500" />
            <text class="text-gray-700">
              {{ institutionDetail.contact.website }}
            </text>
          </view>
          <wd-button
            size="small"
            type="default"
            @click="openWebsite(institutionDetail.contact.website!)"
          >
            访问
          </wd-button>
        </view>
      </view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view v-else class="h-screen flex items-center justify-center">
    <wd-skeleton :row="3" />
  </view>
</template>

<style lang="scss" scoped>
.bg-gray-50 {
  background-color: #f9fafb;
}
</style>
