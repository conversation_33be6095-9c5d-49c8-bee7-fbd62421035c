<route lang="jsonc" type="page">
{
  "layout": "tabbar",
  "style": {
    "navigationBarTitleText": "产业链"
  }
}
</route>

<script setup lang="ts">
// 产业链页面
defineOptions({
  name: 'IndustryChain',
})

// 产业链数据结构
interface IndustryLevel {
  id: string
  name: string
  children: IndustryCategory[]
}

interface IndustryCategory {
  id: string
  name: string
  level: string
  children: IndustryItem[]
}

interface IndustryItem {
  id: string
  name: string
  category: string
  level: string
}

// 静态产业链数据
const industryChain: IndustryLevel[] = [
  {
    id: 'upstream',
    name: '上游（基础层）',
    children: [
      {
        id: 'hardware',
        name: '硬件设备',
        level: '上游',
        children: [
          { id: 'chip', name: '芯片', category: '硬件设备', level: '上游' },
          { id: 'sensor', name: '传感器', category: '硬件设备', level: '上游' },
        ],
      },
      {
        id: 'data-service',
        name: '数据服务',
        level: '上游',
        children: [
          { id: 'big-data', name: '大数据', category: '数据服务', level: '上游' },
          { id: 'cloud-computing', name: '云计算服务', category: '数据服务', level: '上游' },
        ],
      },
    ],
  },
]

// 跳转到企业列表
function goToCompanyList(level: string, category?: string, item?: string) {
  let url = `/pages/industry-chain/company-list?level=${encodeURIComponent(level)}`
  if (category)
    url += `&category=${encodeURIComponent(category)}`
  if (item)
    url += `&item=${encodeURIComponent(item)}`

  uni.navigateTo({
    url,
  })
}
</script>

<template>
  <view class="min-h-screen bg-gray-50">
    <!-- 页面头部 -->
    <view class="bg-white px-4 py-3 shadow-sm">
      <text class="text-xl text-gray-800 font-bold">
        产业链结构
      </text>
    </view>

    <!-- 产业链层次展示 -->
    <view class="p-4">
      <view v-for="level in industryChain" :key="level.id" class="industry-level mb-6">
        <!-- 层次标题 -->
        <view class="level-header rounded-t-xl from-[#018d71] to-[#016853] bg-gradient-to-r p-4 text-white">
          <view class="flex items-center">
            <text class="i-carbon-chart-line mr-2 text-lg" />
            <text class="text-lg font-semibold">
              {{ level.name }}
            </text>
          </view>
        </view>

        <!-- 分类列表 -->
        <view class="level-content rounded-b-xl bg-white p-4 shadow-sm">
          <view v-for="category in level.children" :key="category.id" class="category-group mb-5">
            <!-- 分类标题 -->
            <view
              class="category-title flex cursor-pointer items-center justify-between rounded-lg bg-gray-50 p-3 transition-all duration-200 hover:bg-[#e6f7f4] hover:text-[#018d71]"
              @click="goToCompanyList(category.level, category.name)"
            >
              <view class="flex items-center">
                <text class="i-carbon-cube mr-2 text-[#018d71]" />
                <text class="text-md font-medium">
                  {{ category.name }}
                </text>
              </view>
              <text class="i-carbon-chevron-right text-gray-500" />
            </view>

            <!-- 项目列表 -->
            <view class="item-list mt-2 pl-8">
              <view
                v-for="item in category.children"
                :key="item.id"
                class="item mt-2 flex cursor-pointer items-center justify-between rounded-lg bg-white p-2 transition-all duration-200 hover:bg-[#e6f7f4] hover:text-[#018d71]"
                @click="goToCompanyList(item.level, item.category, item.name)"
              >
                <view class="flex items-center">
                  <text class="i-carbon-dot-mark mr-2 text-xs text-[#018d71]" />
                  <text class="text-sm">
                    {{ item.name }}
                  </text>
                </view>
                <text class="i-carbon-chevron-right text-xs text-gray-400" />
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 空状态提示 -->
    <view v-if="industryChain.length === 0" class="flex flex-col items-center justify-center py-20">
      <text class="i-carbon-chart-line mb-4 text-4xl text-gray-300" />
      <text class="text-sm text-gray-500">
        暂无产业链数据
      </text>
    </view>
  </view>
</template>

<style scoped>
.level-header {
  box-shadow: 0 2px 4px rgba(1, 141, 113, 0.1);
}

.level-content {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.category-title,
.item {
  transition: all 0.2s ease;
}

.category-title:hover,
.item:hover {
  transform: translateX(2px);
}
</style>
