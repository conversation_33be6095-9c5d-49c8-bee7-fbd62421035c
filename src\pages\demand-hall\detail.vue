<route lang="jsonc" type="page">
{
  "style": {
    "navigationBarTitleText": "需求详情"
  }
}
</route>

<script setup lang="ts">
import { onLoad } from '@dcloudio/uni-app'
import { ref } from 'vue'

// 技术需求详情数据接口
interface TechDemandDetail {
  id: number
  requester: string
  content: string
  time: string
  category: string
  budget: string
  contact: string
  status: string
  description: string
  requirements: string
  deadline: string
  location: string
}

const demandDetail = ref<TechDemandDetail>({
  id: 0,
  requester: '',
  content: '',
  time: '',
  category: '',
  budget: '',
  contact: '',
  status: '',
  description: '',
  requirements: '',
  deadline: '',
  location: '',
})

// 模拟需求详情数据
const demandDetailData: Record<number, TechDemandDetail> = {
  1: {
    id: 1,
    requester: '张工程师',
    content: '寻求工业自动化控制系统的技术合作',
    time: '2小时前',
    category: '工业自动化',
    budget: '10-20万',
    contact: '张工 - 13800138000',
    status: '待对接',
    description: '我们需要开发一套完整的工业自动化控制系统，用于生产线上的物料搬运和装配流程。系统需要集成PLC控制、机器视觉检测和机械臂协同作业。',
    requirements: '1. 5年以上工业自动化项目经验\n2. 熟悉西门子/三菱PLC编程\n3. 具备机器视觉系统集成能力\n4. 有机械臂控制经验者优先',
    deadline: '2024-12-31',
    location: '广东省深圳市',
  },
  2: {
    id: 2,
    requester: '李技术总监',
    content: '需要AI图像识别算法的技术支援',
    time: '5小时前',
    category: '人工智能',
    budget: '15-30万',
    contact: '李总 - 13900139000',
    status: '洽谈中',
    description: '我们正在开发产品质量检测系统，需要对产品表面缺陷进行自动识别和分类。目前需要AI图像识别算法的技术支援，提高检测准确率。',
    requirements: '1. 深度学习图像识别经验\n2. 熟悉TensorFlow/PyTorch框架\n3. 有工业视觉检测项目经验\n4. 能够提供算法优化方案',
    deadline: '2024-11-30',
    location: '江苏省苏州市',
  },
  3: {
    id: 3,
    requester: '王项目经理',
    content: '寻找新能源电池材料研发合作伙伴',
    time: '1天前',
    category: '新能源',
    budget: '50-100万',
    contact: '王经理 - 13700137000',
    status: '待对接',
    description: '我们计划开发新一代高能量密度锂离子电池，需要寻找在正极材料研发方面有丰富经验的合作伙伴，共同推进项目进展。',
    requirements: '1. 材料科学或化学工程相关专业\n2. 锂离子电池正极材料研发经验\n3. 具备实验室研发和小批量生产能力\n4. 有产学研合作经验者优先',
    deadline: '2025-03-31',
    location: '上海市浦东新区',
  },
}

onLoad((options) => {
  const id = Number(options?.id) || 1
  demandDetail.value = demandDetailData[id] || demandDetailData[1]
})

// 返回上一页
function goBack() {
  uni.navigateBack()
}

// 联系需求方
function contactRequester() {
  const phoneNumber = demandDetail.value.contact.split('-')[1]?.trim()
  if (phoneNumber) {
    uni.makePhoneCall({
      phoneNumber,
    })
  }
}
</script>

<template>
  <view class="min-h-screen bg-white pb-20">
    <!-- 返回按钮 -->
    <view class="px-4 pt-4">
      <wd-icon name="arrow-left" size="20" @click="goBack" />
    </view>

    <!-- 需求基本信息 -->
    <view class="px-4 pt-4">
      <text class="block text-xl text-gray-800 font-bold">
        {{ demandDetail.content }}
      </text>
      <view class="mt-3 flex items-center justify-between">
        <text class="text-base text-blue-600 font-medium">
          {{ demandDetail.requester }}
        </text>
        <text class="text-sm text-gray-500">
          {{ demandDetail.time }}
        </text>
      </view>
    </view>

    <!-- 详细信息卡片 -->
    <view class="px-4 pt-6">
      <view class="rounded-lg bg-gray-50 p-4 space-y-4">
        <view class="flex items-center">
          <text class="w-20 text-gray-500">
            需求分类：
          </text>
          <text class="flex-1 font-medium">
            {{ demandDetail.category }}
          </text>
        </view>

        <view class="flex items-center">
          <text class="w-20 text-gray-500">
            项目预算：
          </text>
          <text class="flex-1 text-green-600 font-medium">
            {{ demandDetail.budget }}
          </text>
        </view>

        <view class="flex items-center">
          <text class="w-20 text-gray-500">
            截止时间：
          </text>
          <text class="flex-1">
            {{ demandDetail.deadline }}
          </text>
        </view>

        <view class="flex items-center">
          <text class="w-20 text-gray-500">
            项目地点：
          </text>
          <text class="flex-1">
            {{ demandDetail.location }}
          </text>
        </view>

        <view class="flex items-center">
          <text class="w-20 text-gray-500">
            当前状态：
          </text>
          <text
            :class="{
              'text-green-600': demandDetail.status === '已对接',
              'text-blue-600': demandDetail.status === '洽谈中',
              'text-orange-600': demandDetail.status === '待对接',
            }"
            class="flex-1 font-medium"
          >
            {{ demandDetail.status }}
          </text>
        </view>
      </view>
    </view>

    <!-- 需求描述 -->
    <view class="px-4 pt-6">
      <text class="mb-2 block text-gray-800 font-medium">
        需求描述
      </text>
      <text class="block text-gray-600 leading-relaxed">
        {{ demandDetail.description }}
      </text>
    </view>

    <!-- 技术要求 -->
    <view class="px-4 pt-6">
      <text class="mb-2 block text-gray-800 font-medium">
        技术要求
      </text>
      <text class="block whitespace-pre-line text-gray-600 leading-relaxed">
        {{ demandDetail.requirements }}
      </text>
    </view>

    <!-- 联系方式 -->
    <view class="px-4 pt-6">
      <view class="rounded-lg bg-blue-50 p-4">
        <text class="mb-2 block text-blue-600 font-medium">
          联系方式
        </text>
        <text class="text-blue-800">
          {{ demandDetail.contact }}
        </text>
        <view class="mt-3">
          <wd-button type="primary" size="small" @click="contactRequester">
            立即联系
          </wd-button>
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.whitespace-pre-line {
  white-space: pre-line;
}
</style>
