<script setup lang="ts">
defineOptions({
  name: 'Navbar',
})
withDefaults(defineProps<{
  title: string
  transparent?: boolean
}>(), {
  transparent: false,
})

function uniBack() {
  uni.navigateBack()
}
</script>

<template>
  <wd-navbar
    :custom-style="transparent ? 'background-color: transparent !important;' : ''"
    safe-area-inset-top placeholder fixed
    :bordered="false"
    @click-left="uniBack"
  >
    <template #left>
      <view class="i-carbon-drill-back text-black" />
    </template>
    <template #title>
      <text class="font-500">
        {{ title }}
      </text>
    </template>
  </wd-navbar>
</template>
