import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { PolicyVo, PolicyForm, PolicyListQueryRequest } from './types';

/**
 * 查询政策列表
 * @param query 查询参数
 * @returns {*}
 */
export const listPolicy = (query?: PolicyListQueryRequest): AxiosPromise<PolicyVo[]> => {
  return request({
    url: '/biz/policy/list',
    method: 'get',
    params: query
  });
};

/**
 * 获取政策详细信息
 * @param id 政策ID
 */
export const getPolicy = (id: string | number): AxiosPromise<PolicyVo> => {
  return request({
    url: '/biz/policy/' + id,
    method: 'get'
  });
};

/**
 * 新增政策
 * @param data 政策数据
 */
export const addPolicy = (data: PolicyForm) => {
  return request({
    url: '/biz/policy',
    method: 'post',
    data: data
  });
};

/**
 * 修改政策
 * @param data 政策数据
 */
export const updatePolicy = (data: PolicyForm) => {
  return request({
    url: '/biz/policy',
    method: 'put',
    data: data
  });
};

/**
 * 删除政策
 * @param ids 政策ID，多个用逗号分隔
 */
export const delPolicy = (ids: string | number) => {
  return request({
    url: '/biz/policy/' + ids,
    method: 'delete'
  });
};

/**
 * 导出政策列表
 * @param query 查询参数
 */
export const exportPolicy = (query: PolicyListQueryRequest) => {
  return request({
    url: '/biz/policy/export',
    method: 'post',
    data: query
  });
};