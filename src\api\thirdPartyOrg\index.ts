import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { PolicyVo, PolicyForm, PolicyListQueryRequest } from './types';

// "name": "修改第三方机构",
// "method": "put",
// "path": "/biz/thirdPartyOrg",

// "name": "新增第三方机构",
// "method": "post",
// "path": "/biz/thirdPartyOrg",

// "name": "导出第三方机构列表",
// "method": "post",
// "path": "/biz/thirdPartyOrg/export",

// "name": "获取第三方机构详细信息",
// "method": "get",
// "path": "/biz/thirdPartyOrg/{id}",

// "name": "查询第三方机构列表",
// "method": "get",
// "path": "/biz/thirdPartyOrg/list",

// "name": "删除第三方机构",
// "method": "delete",
// "path": "/biz/thirdPartyOrg/{ids}",