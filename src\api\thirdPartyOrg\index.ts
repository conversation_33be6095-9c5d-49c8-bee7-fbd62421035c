import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { ThirdPartyOrgVo, ThirdPartyOrgForm, ThirdPartyOrgListQueryRequest } from './types';

/**
 * 查询第三方机构列表
 * @param query 查询参数
 * @returns {*}
 */
export const listThirdPartyOrg = (query?: ThirdPartyOrgListQueryRequest): AxiosPromise<ThirdPartyOrgVo[]> => {
  return request({
    url: '/biz/thirdPartyOrg/list',
    method: 'get',
    params: query
  });
};

/**
 * 获取第三方机构详细信息
 * @param id 第三方机构ID
 */
export const getThirdPartyOrg = (id: string | number): AxiosPromise<ThirdPartyOrgVo> => {
  return request({
    url: '/biz/thirdPartyOrg/' + id,
    method: 'get'
  });
};

/**
 * 新增第三方机构
 * @param data 第三方机构数据
 */
export const addThirdPartyOrg = (data: ThirdPartyOrgForm) => {
  return request({
    url: '/biz/thirdPartyOrg',
    method: 'post',
    data: data
  });
};

/**
 * 修改第三方机构
 * @param data 第三方机构数据
 */
export const updateThirdPartyOrg = (data: ThirdPartyOrgForm) => {
  return request({
    url: '/biz/thirdPartyOrg',
    method: 'put',
    data: data
  });
};

/**
 * 删除第三方机构
 * @param ids 第三方机构ID，多个用逗号分隔
 */
export const delThirdPartyOrg = (ids: string | number) => {
  return request({
    url: '/biz/thirdPartyOrg/' + ids,
    method: 'delete'
  });
};

/**
 * 导出第三方机构列表
 * @param query 查询参数
 */
export const exportThirdPartyOrg = (query: ThirdPartyOrgListQueryRequest) => {
  return request({
    url: '/biz/thirdPartyOrg/export',
    method: 'post',
    data: query
  });
};