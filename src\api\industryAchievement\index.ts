import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { ThirdPartyOrgVo, ThirdPartyOrgForm, ThirdPartyOrgListQueryRequest } from './types';


// "name": "修改产业成果",
// "method": "put",
// "path": "/biz/industryAchievement",

// "name": "新增产业成果",
// "method": "post",
// "path": "/biz/industryAchievement",

// "name": "导出产业成果列表",
// "method": "post",
// "path": "/biz/industryAchievement/export",

// "name": "获取产业成果详细信息",
// "method": "get",
// "path": "/biz/industryAchievement/{id}",

// "name": "查询产业成果列表",
// "method": "get",
// "path": "/biz/industryAchievement/list",

// "name": "删除产业成果",
// "method": "delete",
// "path": "/biz/industryAchievement/{ids}",