import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { IndustryAchievementVo, IndustryAchievementForm, IndustryAchievementListQueryRequest } from './types';

/**
 * 查询产业成果列表
 * @param query 查询参数
 * @returns {*}
 */
export const listIndustryAchievement = (query?: IndustryAchievementListQueryRequest): AxiosPromise<IndustryAchievementVo[]> => {
  return request({
    url: '/biz/industryAchievement/list',
    method: 'get',
    params: query
  });
};

/**
 * 获取产业成果详细信息
 * @param id 产业成果ID
 */
export const getIndustryAchievement = (id: string | number): AxiosPromise<IndustryAchievementVo> => {
  return request({
    url: '/biz/industryAchievement/' + id,
    method: 'get'
  });
};

/**
 * 新增产业成果
 * @param data 产业成果数据
 */
export const addIndustryAchievement = (data: IndustryAchievementForm) => {
  return request({
    url: '/biz/industryAchievement',
    method: 'post',
    data: data
  });
};

/**
 * 修改产业成果
 * @param data 产业成果数据
 */
export const updateIndustryAchievement = (data: IndustryAchievementForm) => {
  return request({
    url: '/biz/industryAchievement',
    method: 'put',
    data: data
  });
};

/**
 * 删除产业成果
 * @param ids 产业成果ID，多个用逗号分隔
 */
export const delIndustryAchievement = (ids: string | number) => {
  return request({
    url: '/biz/industryAchievement/' + ids,
    method: 'delete'
  });
};

/**
 * 导出产业成果列表
 * @param query 查询参数
 */
export const exportIndustryAchievement = (query: IndustryAchievementListQueryRequest) => {
  return request({
    url: '/biz/industryAchievement/export',
    method: 'post',
    data: query
  });
};