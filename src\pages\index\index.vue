<!-- 使用 type="home" 属性设置首页，其他页面不需要设置，默认为page -->
<route lang="jsonc" type="home">
{
  "layout": "tabbar",
  "style": {
    // 'custom' 表示开启自定义导航栏，默认 'default'
    "navigationStyle": "custom",
    "navigationBarTitleText": "科创通"
  }
}
</route>

<script lang="ts" setup>
import { ref } from 'vue'
// import PLATFORM from '@/utils/platform'

defineOptions({
  name: 'Home',
})

// 新闻数据类型定义
interface NewsItem {
  id: number
  title: string
  date: string
  imageSrc?: string
}

// 响应式新闻数据（初始化为空数组模拟无数据状态）
const newsList = ref<NewsItem[]>([])

// 演示函数：切换数据状态（用于测试）
function toggleNewsData() {
  if (newsList.value.length === 0) {
    // 添加模拟数据
    newsList.value = [
      {
        id: 1,
        title: '星际荣耀双曲线一号遥十运载火箭发射成功',
        date: '2021-09-01',
        imageSrc: 'https://picsum.photos/80',
      },
      {
        id: 2,
        title: '中国航天科技集团发布新一代运载火箭计划',
        date: '2021-09-15',
        imageSrc: 'https://picsum.photos/80',
      },
      {
        id: 3,
        title: '民营航天企业突破关键技术，实现商业化运营',
        date: '2021-10-01',
        imageSrc: 'https://picsum.photos/80',
      },
    ]
  }
  else {
    // 清空数据
    newsList.value = []
  }
}
onMounted(toggleNewsData)

// 获取屏幕边界到安全区域距离
// let safeAreaInsets
// let systemInfo

// 跳转到搜索页面
function goToSearch() {
  uni.navigateTo({
    url: '/pages/search/index',
  })
}

// 跳转到资讯页面
function goToNews() {
  uni.navigateTo({
    url: '/pages/news/index',
  })
}

// 跳转到AI问答页面
function goToAIChat() {
  uni.navigateTo({
    url: '/pages/ai-chat/index',
  })
}

// 跳转到咨询建议页面
function goToConsultation() {
  uni.navigateTo({
    url: '/pages/consultation/index',
  })
}

// 跳转到需求大厅页面
function goToDemandHall() {
  uni.navigateTo({
    url: '/pages/demand-hall/index',
  })
}

// 跳转到科技成果页面
function goToTechAchievement() {
  uni.navigateTo({
    url: '/pages/tech-achievement/index',
  })
}

// #ifdef MP-WEIXIN
// 微信小程序使用新的API
// systemInfo = uni.getWindowInfo()
// safeAreaInsets = systemInfo.safeArea
//   ? {
//       top: systemInfo.safeArea.top,
//       right: systemInfo.windowWidth - systemInfo.safeArea.right,
//       bottom: systemInfo.windowHeight - systemInfo.safeArea.bottom,
//       left: systemInfo.safeArea.left,
//     }
//   : null
// #endif

// #ifndef MP-WEIXIN
// 其他平台继续使用uni API
// systemInfo = uni.getSystemInfoSync()
// safeAreaInsets = systemInfo.safeAreaInsets
// #endif

// 跳转到资讯详情页面
function goToNewsDetail(id: number) {
  uni.navigateTo({
    url: `/pages/news/detail?id=${id}`,
  })
}
</script>

<template>
  <view class="home-page">
    <image
      class="home-bg"
      src="/static/images/home/<USER>"
      mode="scaleToFill"
    />
    <view class="home-content-layer">
      <!-- 搜索框 -->
      <view class="search-section">
        <input type="text" placeholder="大数据产业最新政策" placeholder-style="color: #bec3cb;" @confirm="goToSearch">
        <view class="search-icon-wrapper" @click="goToSearch">
          <image
            class="search-icon"
            src="/static/images/home/<USER>"
            mode="scaleToFill"
          />
        </view>
      </view>

      <view class="mb-36rpx mt-30rpx">
        <!-- 科技成果、需求大厅 -->
        <view class="menu-section">
          <view class="menu-item-wrap">
            <view class="technology menu-item" @click="goToTechAchievement">
              <image
                class="menu-icon"
                src="/static/images/home/<USER>"
                mode="scaleToFill"
              />
              科技成果
            </view>
          </view>

          <view class="menu-item-wrap">
            <view class="menu-item requirement" @click="goToDemandHall">
              <image
                class="menu-icon"
                src="/static/images/home/<USER>"
                mode="scaleToFill"
              />
              需求大厅
            </view>
          </view>
        </view>

        <!-- ai 问答、 咨询与建议 -->
        <view class="menu-section mt-16rpx">
          <view class="menu-secondary-item-wrap">
            <view class="ask menu-secondary-item" @click="goToAIChat">
              <image
                class="text-image"
                src="/static/images/home/<USER>"
                mode="scaleToFill"
              />
              <image
                class="menu-image"
                src="/static/images/home/<USER>"
                mode="scaleToFill"
              />
            </view>
          </view>

          <view class="menu-secondary-item-wrap">
            <view class="menu-secondary-item suggest" @click="goToConsultation">
              <image
                class="text-image"
                src="/static/images/home/<USER>"
                mode="scaleToFill"
              />
              <image
                class="menu-image"
                src="/static/images/home/<USER>"
                mode="scaleToFill"
              />
            </view>
          </view>
        </view>
      </view>
      <!-- 最新资讯 -->
      <view class="news-section">
        <view class="section-title mb-2 flex items-center justify-between">
          <text>
            最新资讯
          </text>
          <view class="more-btn" @click="goToNews">
            <image
              class="mr-10rpx size-40rpx"
              src="/static/images/home/<USER>"
              mode="scaleToFill"
            /><text class="vertical-middle">
              更多
            </text>
          </view>
        </view>
        <view class="news-list">
          <template v-if="newsList.length">
            <view v-for="news in newsList" :key="news.id" class="mt-16rpx">
              <real-time-info-card :title="news.title" :date="news.date" :image-src="news.imageSrc" @click="() => goToNewsDetail(news.id)" />
            </view>
          </template>
          <template v-else>
            <EmptyState
              text="暂无最新资讯"
              padding="80rpx 0"
            />
          </template>
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.home-page {
  width: 100%;
  background: #f4f6fa;
}

.home-bg {
  height: 560rpx;
}

.home-content-layer {
  position: relative;
  padding: 0 30rpx;
  top: -180rpx;
}

.search-section {
  background-color: #ffffff;
  // box-shadow: 0rpx 2rpx 4rpx 0rpx rgba(41, 80, 139, 0.2);
  border-radius: 36rpx;
  padding: 16rpx 32rpx;
  display: flex;
  align-items: center;

  input {
    flex: 1;
    font-size: 28rpx;
  }

  .search-icon-wrapper {
    width: 60rpx;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    cursor: pointer;
  }

  .search-icon {
    width: 40rpx;
    height: 40rpx;
  }
}

.menu-section {
  display: flex;
  justify-content: space-between;

  .menu-item-wrap {
    padding-top: 32rpx;
    position: relative;
  }

  .menu-item {
    width: 312rpx;
    height: 128rpx;
    line-height: 128rpx;
    padding-left: 144rpx;
    border-radius: 32rpx;
    border: 2rpx solid rgba(255, 255, 255, 0.9);
    box-sizing: border-box;

    &.technology {
      background: linear-gradient(180deg, #89cbf6 0%, rgba(137, 203, 246, 0.23) 100%);
      font-size: 32rpx;
      font-weight: 500;
      color: #0e4fb1;
    }

    &.requirement {
      background: linear-gradient(180deg, #778fff 0%, rgba(127, 150, 254, 0.19) 100%);
      color: #0018d2;
    }

    .menu-icon {
      width: 144rpx;
      height: 144rpx;
      position: absolute;
      top: 0;
      left: 0;
    }
  }

  .menu-secondary-item-wrap {
    padding-top: 60rpx;
    position: relative;
  }

  .menu-secondary-item {
    width: 312rpx;
    height: 88rpx;
    border-radius: 44rpx;
    .text-image {
      position: absolute;
      top: 0;
      left: 0;
      width: 186rpx;
      height: 148rpx;
    }

    .menu-image {
      position: absolute;
      top: 0;
      right: 0;
      width: 128rpx;
      height: 148rpx;
    }
    &.suggest {
      background: #dceff8;
    }

    &.ask {
      background: #dce7f8;
    }
  }
}

.section-title {
  font-weight: bold;
  font-size: 40rpx;
}
.more-btn {
  font-size: 28rpx;
  line-height: 40rpx;
  color: #969ba4;
  font-weight: normal;
}

.news-list {
  margin-top: 42rpx;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 0;
  text-align: center;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 32rpx;
  opacity: 0.6;
}

.empty-text {
  font-size: 28rpx;
  color: #969ba4;
  line-height: 40rpx;
}
</style>
