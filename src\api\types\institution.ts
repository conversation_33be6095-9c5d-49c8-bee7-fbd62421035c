/**
 * 机构数据类型定义
 */
export interface Institution {
  id: number
  name: string
  type: '金融' | '法律' | '咨询' | '技术'
  logo: string
  description: string
  services: string[]
  cases: Case[]
  contact: ContactInfo
}

/**
 * 成功案例
 */
export interface Case {
  title: string
  description: string
  result: string
}

/**
 * 联系信息
 */
export interface ContactInfo {
  phone: string
  email: string
  address?: string
  website?: string
}

/**
 * 机构列表请求参数
 */
export interface InstitutionListParams {
  type?: string
  keyword?: string
  page?: number
  pageSize?: number
}

/**
 * 机构列表响应数据
 */
export interface InstitutionListResponse {
  list: Institution[]
  total: number
  page: number
  pageSize: number
}
