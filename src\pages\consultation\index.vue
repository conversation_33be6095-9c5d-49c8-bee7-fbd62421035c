<route lang="jsonc" type="page">
{
  "style": {
    "navigationBarTitleText": "咨询与建议",
    "navigationStyle": "custom"
  }
}
</route>

<script setup lang="ts">
// 咨询与建议页面

defineOptions({
  name: 'Consultation',
})

// 模拟数据状态，实际项目中应该从store或api获取
const hasData = false // 当没有数据时显示空状态
</script>

<template>
  <view class="consultation-page">
    <navabar title="咨询与建议" transparent />

    <!-- 有数据时显示内容 -->
    <template v-if="hasData">
      <image
        class="consultation-bg"
        src="/static/images/consultation/consultation-bg.png"
        mode="scaleToFill"
      />
      <view class="consultation-content-layer">
        <view class="consultation-card">
          <view class="questioner">
            <view>
              <image
                class="mr-8px size-20px"
                src="/static/images/consultation/avatar.png"
                mode="scaleToFill"
              />
              <text class="questioner-name">
                提问人姓***名
              </text>
            </view>
            <text class="ask-time">
              2025-08-22 10:22
            </text>
          </view>
          <view class="divider" />
          <view class="question-desc">
            问题描述
          </view>
        </view>
      </view>
    </template>

    <!-- 无数据时显示空状态 -->
    <template v-else>
      <EmptyState
        text="暂无咨询记录"
        subtext="您还没有任何咨询记录"
        padding="200rpx 0"
      />
    </template>
  </view>
</template>

<style lang="scss" scoped>
.consultation-page {
  width: 100%;
  background: #f4f6fa;
}

.consultation-bg {
  height: 700rpx;
}

.consultation-content-layer {
  position: relative;
  padding: 0 32rpx;
  top: -668rpx;
}

.consultation-card {
  background: #ffffff;
  border-radius: 12px;
  padding: 14px 16px;

  .questioner {
    display: flex;
    justify-content: space-between;
    align-items: center;

    &-name {
      font-size: 14px;
      line-height: 20px;
      vertical-align: middle;
    }

    .ask-time {
      color: #94989f;
      font-size: 14px;
    }
  }

  .divider {
    height: 2rpx;
    background: #e5e7eb;
    margin: 14px 0;
  }

  .question-desc {
    font-size: 14px;
    color: #686d76;
    line-height: 20px;
  }
}
</style>
