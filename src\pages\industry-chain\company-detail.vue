<route lang="jsonc" type="page">
{
  "style": {
    "navigationBarTitleText": "企业详情"
  }
}
</route>

<script setup lang="ts">
import { onMounted, ref } from 'vue'

interface Company {
  id: string
  name: string
  level: string
  category: string
  item: string
  description: string
  logo: string
  contact: string
  address?: string
  website?: string
  established?: string
  employees?: string
}

defineOptions({
  name: 'CompanyDetail',
})

// 静态企业数据
const companies: Company[] = [
  {
    id: '1',
    name: '华为技术有限公司',
    level: '上游',
    category: '硬件设备',
    item: '芯片',
    description: '全球领先的信息与通信技术解决方案供应商',
    logo: '/static/images/default-avatar.png',
    contact: '400-830-8300',
    address: '广东省深圳市龙岗区坂田华为基地',
    website: 'www.huawei.com',
    established: '1987年',
    employees: '19.5万人',
  },
  {
    id: '2',
    name: '中芯国际',
    level: '上游',
    category: '硬件设备',
    item: '芯片',
    description: '中国内地规模最大、技术最先进的集成电路芯片制造企业',
    logo: '/static/images/default-avatar.png',
    contact: '021-6101-8888',
    address: '上海市浦东新区张江高科技园区',
    website: 'www.smics.com',
    established: '2000年',
    employees: '1.7万人',
  },
  {
    id: '3',
    name: '海康威视',
    level: '上游',
    category: '硬件设备',
    item: '传感器',
    description: '全球领先的安防产品及行业解决方案提供商',
    logo: '/static/images/default-avatar.png',
    contact: '400-700-5998',
    address: '浙江省杭州市滨江区阡陌路555号',
    website: 'www.hikvision.com',
    established: '2001年',
    employees: '5.2万人',
  },
  {
    id: '4',
    name: '大华股份',
    level: '上游',
    category: '硬件设备',
    item: '传感器',
    description: '全球领先的智慧物联解决方案提供商和运营服务商',
    logo: '/static/images/default-avatar.png',
    contact: '400-672-8166',
    address: '浙江省杭州市滨江区滨安路1199号',
    website: 'www.dahuatech.com',
    established: '2001年',
    employees: '1.8万人',
  },
  {
    id: '5',
    name: '阿里云',
    level: '上游',
    category: '数据服务',
    item: '云计算服务',
    description: '全球领先的云计算及人工智能科技公司',
    logo: '/static/images/default-avatar.png',
    contact: '95187',
    address: '浙江省杭州市西湖区文一西路969号',
    website: 'www.aliyun.com',
    established: '2009年',
    employees: '2.5万人',
  },
  {
    id: '6',
    name: '腾讯云',
    level: '上游',
    category: '数据服务',
    item: '云计算服务',
    description: '腾讯集团倾力打造的云计算品牌',
    logo: '/static/images/default-avatar.png',
    contact: '4009-100-100',
    address: '广东省深圳市南山区深南大道10000号腾讯大厦',
    website: 'cloud.tencent.com',
    established: '2013年',
    employees: '1.2万人',
  },
  {
    id: '7',
    name: '百度智能云',
    level: '上游',
    category: '数据服务',
    item: '大数据',
    description: '百度推出的智能云计算服务',
    logo: '/static/images/default-avatar.png',
    contact: '400-890-0088',
    address: '北京市海淀区上地十街10号百度大厦',
    website: 'cloud.baidu.com',
    established: '2015年',
    employees: '0.8万人',
  },
  {
    id: '8',
    name: '华为云',
    level: '上游',
    category: '数据服务',
    item: '大数据',
    description: '华为公司推出的云计算服务',
    logo: '/static/images/default-avatar.png',
    contact: '950808',
    address: '广东省深圳市龙岗区坂田华为基地',
    website: 'www.huaweicloud.com',
    established: '2017年',
    employees: '1.5万人',
  },
]

const currentCompany = ref<Company | null>(null)

// 从路由参数获取企业ID
onMounted(() => {
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1] as any
  const options = currentPage?.options || {}
  const companyId = decodeURIComponent(options.id || '')

  if (companyId) {
    const company = companies.find(c => c.id === companyId)
    if (company) {
      currentCompany.value = company
    }
  }
})

// 拨打电话
function makePhoneCall(phoneNumber: string) {
  uni.makePhoneCall({
    phoneNumber,
  })
}
</script>

<template>
  <view v-if="currentCompany" class="bg-white">
    <!-- 企业头部信息 -->
    <view class="company-header from-blue-50 to-green-50 bg-gradient-to-r p-4">
      <view class="mb-4 flex items-center">
        <image :src="currentCompany.logo" class="mr-4 h-16 w-16 rounded-lg" />
        <view class="flex-1">
          <text class="mb-1 block text-xl font-bold">
            {{ currentCompany.name }}
          </text>
          <text class="block text-sm text-gray-600">
            {{ currentCompany.description }}
          </text>
        </view>
      </view>
    </view>

    <!-- 企业详细信息 -->
    <view class="company-details p-4">
      <wd-cell-group title="基本信息" border>
        <wd-cell title="产业链层次" :value="currentCompany.level" />
        <wd-cell title="产业分类" :value="currentCompany.category" />
        <wd-cell title="具体项目" :value="currentCompany.item" />
        <wd-cell title="成立时间" :value="currentCompany.established" />
        <wd-cell title="员工规模" :value="currentCompany.employees" />
      </wd-cell-group>

      <wd-cell-group title="联系信息" class="mt-4" border>
        <wd-cell title="联系电话" :value="currentCompany.contact" />
        <wd-cell title="公司地址" :value="currentCompany.address" />
        <wd-cell title="官方网站" :value="currentCompany.website" />
      </wd-cell-group>
    </view>

    <!-- 操作按钮 -->
    <view class="p-4">
      <wd-button type="primary" block @click="makePhoneCall(currentCompany.contact)">
        <text class="i-carbon-phone mr-2" />
        拨打电话
      </wd-button>
    </view>
  </view>

  <view v-else class="h-screen flex items-center justify-center">
    <text class="text-gray-400">
      企业信息不存在
    </text>
  </view>
</template>

<style scoped>
.company-header {
  border-bottom: 1px solid #f0f0f0;
}
</style>
