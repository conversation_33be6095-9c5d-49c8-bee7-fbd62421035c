<script lang="ts" setup>
defineOptions({
  name: 'RealTimeInfoCard',
})

defineProps<{
  title: string
  date: string
  imageSrc?: string
}>()

const emit = defineEmits<{
  (event: 'click'): void
}>()

function handleClick() {
  emit('click')
}
</script>

<template>
  <view class="info-card" @click="handleClick">
    <view class="content">
      <view class="title">
        {{ title }}
      </view>
      <view class="date">
        {{ date }}
      </view>
    </view>
    <view v-if="imageSrc" class="img-wrap">
      <image
        class="size-160rpx rounded-2xl"
        :src="imageSrc"
        mode="aspectFill"
      />
    </view>
  </view>
</template>

<style lang="scss" scoped>
.info-card {
  background: #ffffff;
  // background-color: red;
  border-radius: 24rpx;
  display: flex;

  .content {
    padding: 24rpx;
    line-height: 1.5;

    .title {
      font-size: 32rpx;
      font-weight: 500;
      color: #222b39;
      flex: 1;
      /* 添加以下样式实现超过两行打点 */
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      word-wrap: break-word;
      word-break: break-all;
    }

    .date {
      margin-top: 16rpx;
      font-size: 24rpx;
      font-weight: 400;
      color: #94989f;
    }
  }
  .img-wrap {
    padding: 20rpx;
  }
}
</style>
