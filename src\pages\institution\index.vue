<route lang="jsonc" type="page">
{
  "layout": "tabbar",
  "style": {
    "navigationBarTitleText": "机构"
  }
}
</route>

<script setup lang="ts">
import type { Institution } from '@/api/types/institution'
import { onMounted, ref } from 'vue'

defineOptions({
  name: 'Institution',
})

// 机构类型选项
const institutionTypes = [
  { label: '全部', value: '' },
  { label: '金融', value: '金融' },
  { label: '法律', value: '法律' },
  { label: '咨询', value: '咨询' },
  { label: '技术', value: '技术' },
]

const activeType = ref('')
const searchKeyword = ref('')
const institutionList = ref<Institution[]>([])
const loading = ref(false)

// 模拟机构数据
const mockInstitutions: Institution[] = [
  {
    id: 1,
    name: '中国银行科创金融部',
    type: '金融',
    logo: '/static/images/default-avatar.png',
    description: '专注于科技创新企业的金融服务，提供贷款、投资、咨询等一站式金融解决方案',
    services: ['科创贷款', '风险投资', '金融咨询', '上市辅导'],
    cases: [
      {
        title: '某AI科技公司A轮融资',
        description: '协助完成5000万元A轮融资，估值达到5亿元',
        result: '成功融资，公司快速发展',
      },
    ],
    contact: {
      phone: '************',
      email: '<EMAIL>',
      address: '北京市西城区金融大街1号',
    },
  },
  {
    id: 2,
    name: '金杜律师事务所',
    type: '法律',
    logo: '/static/images/default-avatar.png',
    description: '国际知名的综合性律师事务所，为企业提供全方位的法律服务和解决方案',
    services: ['公司法律', '知识产权', '并购重组', '争议解决'],
    cases: [
      {
        title: '某科技公司IPO项目',
        description: '成功辅导公司在科创板上市，融资规模20亿元',
        result: '成功上市，市值超百亿',
      },
    ],
    contact: {
      phone: '010-5878-5555',
      email: '<EMAIL>',
      website: 'www.kingandwood.com',
    },
  },
  {
    id: 3,
    name: '麦肯锡咨询',
    type: '咨询',
    logo: '/static/images/default-avatar.png',
    description: '全球领先的管理咨询公司，为企业提供战略规划、运营优化等专业咨询服务',
    services: ['战略咨询', '数字化转型', '组织变革', '运营优化'],
    cases: [
      {
        title: '某制造企业数字化转型',
        description: '帮助企业实现数字化升级，提升生产效率30%',
        result: '成功转型，效益显著提升',
      },
    ],
    contact: {
      phone: '021-2308-8888',
      email: '<EMAIL>',
      address: '上海市浦东新区世纪大道8号',
    },
  },
  {
    id: 4,
    name: '华为云技术服务中心',
    type: '技术',
    logo: '/static/images/default-avatar.png',
    description: '提供云计算、人工智能、大数据等前沿技术服务，助力企业数字化转型',
    services: ['云计算', '人工智能', '大数据', '物联网'],
    cases: [
      {
        title: '某政府智慧城市项目',
        description: '建设智慧城市平台，提升城市管理效率50%',
        result: '项目成功落地，获得政府好评',
      },
    ],
    contact: {
      phone: '950808',
      email: '<EMAIL>',
      website: 'www.huaweicloud.com',
    },
  },
]

onMounted(() => {
  loadInstitutions()
})

// 加载机构列表
function loadInstitutions() {
  loading.value = true
  setTimeout(() => {
    institutionList.value = mockInstitutions.filter((institution) => {
      const typeMatch = !activeType.value || institution.type === activeType.value
      const keywordMatch = !searchKeyword.value
        || institution.name.includes(searchKeyword.value)
        || institution.description.includes(searchKeyword.value)
      return typeMatch && keywordMatch
    })
    loading.value = false
  }, 500)
}

// 搜索机构
function handleSearch() {
  loadInstitutions()
}

// 清空搜索
function handleClear() {
  searchKeyword.value = ''
  loadInstitutions()
}

// 跳转到机构详情
function goToDetail(institution: Institution) {
  uni.navigateTo({
    url: `/pages/institution/detail?id=${institution.id}`,
  })
}

// 拨打电话
function makePhoneCall(phone: string) {
  uni.makePhoneCall({
    phoneNumber: phone,
  })
}
</script>

<template>
  <view class="min-h-screen bg-gray-50 pb-20">
    <!-- 搜索区域 -->
    <view class="bg-white px-4 pb-3 pt-4">
      <wd-search
        v-model="searchKeyword"
        placeholder="搜索机构名称或服务"
        @search="handleSearch"
        @clear="handleClear"
      />
    </view>

    <!-- 分类选项卡 -->
    <view class="bg-white px-4 pb-2">
      <wd-tabs v-model="activeType" @change="loadInstitutions">
        <wd-tab
          v-for="type in institutionTypes"
          :key="type.value"
          :name="type.value"
          :title="type.label"
        />
      </wd-tabs>
    </view>

    <!-- 机构列表 -->
    <view class="p-4">
      <wd-skeleton v-if="loading" :row="3" />

      <view v-else class="space-y-4">
        <view
          v-for="institution in institutionList"
          :key="institution.id"
          class="rounded-lg bg-white p-4 shadow-sm"
          @click="goToDetail(institution)"
        >
          <!-- 机构头部信息 -->
          <view class="mb-3 flex items-center">
            <image
              :src="institution.logo"
              class="mr-3 h-12 w-12 rounded-lg"
              mode="aspectFill"
            />
            <view class="flex-1">
              <text class="block text-lg text-gray-800 font-bold">
                {{ institution.name }}
              </text>
              <text class="mt-1 block text-sm text-gray-500">
                {{ institution.type }} · {{ institution.description }}
              </text>
            </view>
          </view>

          <!-- 服务标签 -->
          <view class="mb-3">
            <view class="flex flex-wrap gap-2">
              <text
                v-for="service in institution.services.slice(0, 3)"
                :key="service"
                class="rounded-full bg-blue-50 px-2 py-1 text-xs text-blue-600"
              >
                {{ service }}
              </text>
              <text
                v-if="institution.services.length > 3"
                class="rounded-full bg-gray-100 px-2 py-1 text-xs text-gray-500"
              >
                +{{ institution.services.length - 3 }}
              </text>
            </view>
          </view>

          <!-- 联系方式 -->
          <view class="flex items-center justify-between border-t border-gray-100 pt-3">
            <view class="flex items-center">
              <text class="i-carbon-phone mr-1 text-blue-500" />
              <text class="text-sm text-gray-600">
                {{ institution.contact.phone }}
              </text>
            </view>
            <wd-button
              size="small"
              type="primary"
              plain
              @click.stop="makePhoneCall(institution.contact.phone)"
            >
              联系
            </wd-button>
          </view>
        </view>

        <!-- 空状态 -->
        <view
          v-if="institutionList.length === 0"
          class="py-12 text-center text-gray-400"
        >
          <text class="i-carbon-search mb-2 block text-2xl" />
          <text class="block">
            暂无相关机构
          </text>
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.border-t {
  border-top-width: 1px;
}
</style>
