/**
 * 政策视图对象
 */
export interface PolicyVO extends BaseEntity {
  /**
   * id
   */
  id?: string | number;
  /**
   * 标题
   */
  title?: string;
  /**
   * 内容
   */
  content?: string;
  /**
   * 链接
   */
  url?: string;
  /**
   * 封面
   */
  cover?: string | number;
  /**
   * 封面URL
   */
  coverUrl?: string;
  /**
   * 附件
   */
  attachment?: string;
  /**
   * 附件URL
   */
  attachmentUrl?: string;
}

/**
 * 政策表单对象
 */
export interface PolicyForm extends BaseEntity {
  /**
   * id
   */
  id?: string | number;
  /**
   * 标题
   */
  title?: string;
  /**
   * 内容
   */
  content?: string;
  /**
   * 链接
   */
  url?: string;
  /**
   * 封面
   */
  cover?: string | number;
  /**
   * 附件
   */
  attachment?: string;
}

/**
 * 政策查询对象
 */
export interface PolicyQuery extends PageQuery {
  /**
   * 标题
   */
  title?: string;
  /**
   * 内容
   */
  content?: string;
  /**
   * 链接
   */
  url?: string;
  /**
   * 封面
   */
  cover?: string | number;
  /**
   * 附件
   */
  attachment?: string;
}