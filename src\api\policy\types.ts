/**
 * BizPolicyVo，政策视图对象 biz_policy
 */
export interface PolicyVo {
    /**
     * 附件
     */
    attachment?: string;
    /**
     * 附件
     */
    attachmentUrl?: string;
    /**
     * 内容
     */
    content?: string;
    /**
     * 封面
     */
    cover?: number;
    /**
     * 封面
     */
    coverUrl?: number;
    /**
     * 创建人
     */
    createBy?: string;
    /**
     * 创建时间
     */
    createTime?: Date;
    /**
     * id
     */
    id?: number;
    /**
     * 标题
     */
    title?: string;
    /**
     * 链接
     */
    url?: string;
    [property: string]: any;
}

/**
 * BizPolicyBo，政策业务对象 biz_policy
 */
export interface PolicyForm {
    /**
     * 附件
     */
    attachment?: string;
    /**
     * 内容
     */
    content?: string;
    /**
     * 封面
     */
    cover?: number;
    /**
     * id
     */
    id: number;
    /**
     * 标题
     */
    title: string;
    /**
     * 链接
     */
    url?: string;
    [property: string]: any;
}

export interface PolicyListQueryRequest {
    /**
     * 附件
     */
    attachment?: string;
    /**
     * 内容
     */
    content?: string;
    /**
     * 封面
     */
    cover?: number;
    /**
     * id
     */
    id: number;
    /**
     * 排序的方向desc或者asc
     */
    isAsc?: string;
    /**
     * 排序列
     */
    orderByColumn?: string;
    /**
     * 当前页数
     */
    pageNum?: number;
    /**
     * 分页大小
     */
    pageSize?: number;
    /**
     * 标题
     */
    title: string;
    /**
     * 链接
     */
    url?: string;
    [property: string]: any;
}