<route lang="jsonc" type="page">
{
  "layout": "tabbar",
  "style": {
    "navigationBarTitleText": "发现"
  }
}
</route>

<script setup lang="ts">
// 发现页面
defineOptions({
  name: 'Discovery',
})

// 轮播图数据
const swiperList = [
  {
    url: 'https://picsum.photos/400/200?',
    title: '人工智能芯片技术突破',
    desc: '最新AI芯片研发成果',
  },
  {
    url: 'https://picsum.photos/400/200',
    title: '5G通信技术应用',
    desc: '5G技术在工业领域的创新应用',
  },
  {
    url: 'https://picsum.photos/400/200',
    title: '新能源电池技术',
    desc: '高效能锂电池研发进展',
  },
]

// 产品成果数据
const productList = [
  {
    id: 1,
    image: 'https://picsum.photos/100/100?random=4',
    title: '智能家居控制系统',
    company: '科技股份有限公司',
    desc: '基于物联网的智能家居解决方案',
  },
  {
    id: 2,
    image: 'https://picsum.photos/100/100?random=5',
    title: '工业机器人手臂',
    company: '智能制造有限公司',
    desc: '高精度工业自动化设备',
  },
  {
    id: 3,
    image: 'https://picsum.photos/100/100?random=6',
    title: '医疗影像分析软件',
    company: '医疗科技公司',
    desc: 'AI辅助医疗诊断系统',
  },
]

// 技术需求数据
const demandList = [
  {
    id: 1,
    requester: '张工程师',
    content: '寻求工业自动化控制系统的技术合作',
    time: '2小时前',
  },
  {
    id: 2,
    requester: '李技术总监',
    content: '需要AI图像识别算法的技术支援',
    time: '5小时前',
  },
  {
    id: 3,
    requester: '王项目经理',
    content: '寻找新能源电池材料研发合作伙伴',
    time: '1天前',
  },
]

// 跳转到产品成果列表页面
function goToProductAchievement() {
  uni.navigateTo({
    url: '/pages/product-achievement/index',
  })
}

// 跳转到需求大厅页面
function goToDemandHall() {
  uni.navigateTo({
    url: '/pages/demand-hall/index',
  })
}

// 跳转到产品详情页面
function goToProductDetail(id: number) {
  uni.navigateTo({
    url: `/pages/tech-achievement/detail?id=${id}`,
  })
}

// 跳转到需求详情页面
function goToDemandDetail(id: number) {
  uni.navigateTo({
    url: `/pages/demand-hall/detail?id=${id}`,
  })
}
</script>

<template>
  <view class="min-h-screen bg-white pb-20">
    <!-- 轮播图区域 -->
    <view class="banner-section">
      <wd-swiper
        :list="swiperList"
        autoplay
        indicator
        height="180"
        :show-title="true"
      />
    </view>

    <!-- 产品成果区域 -->
    <view class="product-section px-4 pt-4">
      <view class="section-header mb-3 flex items-center justify-between">
        <text class="text-lg font-bold">
          产品成果
        </text>
        <text class="text-sm text-gray-500" @click="goToProductAchievement">
          更多 >
        </text>
      </view>

      <view class="product-list grid grid-cols-2 gap-3">
        <view
          v-for="product in productList"
          :key="product.id"
          class="product-item flex flex-col rounded-lg bg-gray-50 p-3"
          @click="goToProductDetail(product.id)"
        >
          <image
            :src="product.image"
            class="mb-2 h-24 w-full rounded-md"
            mode="aspectFill"
          />
          <view class="flex-1">
            <text class="line-clamp-2 mb-1 block text-sm font-medium">
              {{ product.title }}
            </text>
            <text class="block text-xs text-gray-600">
              {{ product.company }}
            </text>
          </view>
        </view>
      </view>
    </view>

    <!-- 技术需求区域 -->
    <view class="demand-section px-4 pt-6">
      <view class="section-header mb-3 flex items-center justify-between">
        <text class="text-lg font-bold">
          技术需求
        </text>
        <text class="text-sm text-gray-500" @click="goToDemandHall">
          更多 >
        </text>
      </view>

      <view class="demand-list space-y-3">
        <view
          v-for="demand in demandList"
          :key="demand.id"
          class="demand-item rounded-lg bg-gray-50 p-3"
          @click="goToDemandDetail(demand.id)"
        >
          <view class="mb-2 flex items-center justify-between">
            <text class="text-base font-medium">
              {{ demand.requester }}
            </text>
            <text class="text-xs text-gray-500">
              {{ demand.time }}
            </text>
          </view>
          <text class="text-sm text-gray-600">
            {{ demand.content }}
          </text>
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.product-item,
.demand-item {
  transition: all 0.2s ease;

  &:active {
    background-color: #e5e7eb;
    transform: scale(0.98);
  }
}

.section-header {
  border-bottom: 1px solid #f3f4f6;
  padding-bottom: 8px;
}

// 两列布局优化样式
.product-list {
  .product-item {
    min-height: 180px;

    image {
      object-fit: cover;
    }
  }
}

// 文本溢出处理
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
