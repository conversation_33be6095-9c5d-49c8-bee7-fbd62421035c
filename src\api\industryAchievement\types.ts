/**
 * BizIndustryAchievementVo，产业成果视图对象 biz_industry_achievement
 */
export interface IndustryAchievementVo {
    /**
     * 附件
     */
    attachment?: string;
    /**
     * 附件URL
     */
    attachmentUrl?: string;
    /**
     * 分类
     */
    category?: string;
    /**
     * 公司名称
     */
    companyName?: string;
    /**
     * 内容
     */
    content?: string;
    /**
     * 创建人
     */
    createBy?: string;
    /**
     * 创建时间
     */
    createTime?: Date;
    /**
     * id
     */
    id?: number;
    /**
     * 名称
     */
    name?: string;
    [property: string]: any;
}

/**
 * BizIndustryAchievementBo，产业成果业务对象 biz_industry_achievement
 */
export interface IndustryAchievementForm {
    /**
     * 附件
     */
    attachment: string;
    /**
     * 分类
     */
    category: string;
    /**
     * 公司名称
     */
    companyName: string;
    /**
     * 内容
     */
    content?: string;
    /**
     * id
     */
    id: number;
    /**
     * 名称
     */
    name: string;
    [property: string]: any;
}

export interface IndustryAchievementListQueryRequest {
    /**
     * 附件
     */
    attachment: string;
    /**
     * 分类
     */
    category: string;
    /**
     * 公司名称
     */
    companyName: string;
    /**
     * 内容
     */
    content?: string;
    /**
     * id
     */
    id: number;
    /**
     * 排序的方向desc或者asc
     */
    isAsc?: string;
    /**
     * 名称
     */
    name: string;
    /**
     * 排序列
     */
    orderByColumn?: string;
    /**
     * 当前页数
     */
    pageNum?: number;
    /**
     * 分页大小
     */
    pageSize?: number;
    [property: string]: any;
}