<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="标题" prop="title">
              <el-input v-model="queryParams.title" placeholder="请输入标题" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="内容" prop="content">
              <el-input v-model="queryParams.content" placeholder="请输入内容" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['policy:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['policy:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['policy:remove']">删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['policy:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList" />
        </el-row>
      </template>

      <el-table v-loading="loading" border :data="policyList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="标题" align="center" prop="title" :show-overflow-tooltip="true" />
        <el-table-column label="原始链接" align="center" prop="url" :show-overflow-tooltip="true" />
        <el-table-column label="封面" align="center" prop="coverUrl" width="100">
          <template #default="scope">
            <image-preview v-if="scope.row.coverUrl" :src="scope.row.coverUrl" :width="50" :height="50" />
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="创建时间" align="center" prop="createTime" width="180">
          <template #default="scope">
            <span>{{ proxy?.parseTime(scope.row.createTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="200" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="查看详情" placement="top">
              <el-button link type="primary" icon="View" @click="handleDetail(scope.row)" v-hasPermi="['policy:query']"></el-button>
            </el-tooltip>
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['policy:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['policy:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" :total="total" @pagination="getList" />
    </el-card>

    <!-- 添加或修改政策对话框 -->
    <el-dialog v-model="dialog.visible" :title="dialog.title" width="800px" append-to-body>
      <el-form ref="policyFormRef" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="标题" prop="title">
              <el-input v-model="form.title" placeholder="请输入标题" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="原始链接" prop="url">
              <el-input v-model="form.url" placeholder="请输入原始链接" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="发布时间" prop="publishTime">
              <el-date-picker
                v-model="form.publishTime"
                type="datetime"
                placeholder="选择发布时间"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <!-- todo 封面组件能小一点吗 -->
          <el-col :span="12">
            <el-form-item label="封面">
              <image-upload v-model="form.cover" :limit="1" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="附件">
              <file-upload v-model="form.attachment" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="内容" prop="content">
              <editor v-model="form.content" :min-height="300" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancel">取 消</el-button>
          <el-button type="primary" @click="submitForm">确 定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 政策详情对话框 -->
    <el-dialog v-model="detailDialog.visible" :title="detailDialog.title" width="800px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="标题">{{ detailInfo.title }}</el-descriptions-item>
        <el-descriptions-item label="原始链接">{{ detailInfo.url }}</el-descriptions-item>
        <el-descriptions-item label="封面">
          <image-preview v-if="detailInfo.coverUrl" :src="detailInfo.coverUrl" :width="100" :height="100" />
          <span v-else>-</span>
        </el-descriptions-item>
        <el-descriptions-item label="附件">
          <el-link v-if="detailInfo.attachmentUrl" :href="detailInfo.attachmentUrl" target="_blank" type="primary">
            <el-icon><Download /></el-icon>
            下载附件
          </el-link>
          <span v-else>-</span>
        </el-descriptions-item>
        <el-descriptions-item label="发布时间">{{ detailInfo.publishTime || '-' }}</el-descriptions-item>
        <el-descriptions-item label="创建人">{{ detailInfo.createBy }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ proxy?.parseTime(detailInfo.createTime) }}</el-descriptions-item>
        <el-descriptions-item label="内容" :span="2">
          <div v-html="detailInfo.content" class="policy-content"></div>
        </el-descriptions-item>
      </el-descriptions>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handlePreview" type="primary" icon="View">H5预览</el-button>
          <el-button @click="detailDialog.visible = false">关 闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- H5预览对话框 -->
    <el-dialog v-model="previewDialog.visible" title="H5预览" width="400px" append-to-body>
      <div class="h5-preview-container">
        <div class="mobile-frame">
          <div class="mobile-header">
            <div class="mobile-title">{{ detailInfo.title }}</div>
          </div>
          <div class="mobile-content">
            <div v-if="detailInfo.coverUrl" class="mobile-cover">
              <img :src="detailInfo.coverUrl" alt="封面" />
            </div>
            <div class="mobile-body">
              <div v-html="detailInfo.content" class="mobile-text"></div>
              <div v-if="detailInfo.attachmentUrl" class="mobile-attachment">
                <el-link :href="detailInfo.attachmentUrl" target="_blank" type="primary">
                  <el-icon><Download /></el-icon>
                  下载附件
                </el-link>
              </div>
            </div>
          </div>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="previewDialog.visible = false">关 闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Policy" lang="ts">
import { listPolicy, getPolicy, delPolicy, addPolicy, updatePolicy, exportPolicy } from '@/api/policy';
import { PolicyVo, PolicyQuery, PolicyForm } from '@/api/policy/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const policyList = ref<PolicyVo[]>([]);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const policyFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const detailDialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const previewDialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: PolicyForm = {
  id: undefined,
  title: '',
  content: '',
  url: '',
  cover: undefined,
  attachment: '',
  publishTime: ''
};

const data = reactive<PageData<PolicyForm, PolicyQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    title: '',
    content: '',
    url: '',
    cover: undefined,
    attachment: ''
  },
  rules: {
    title: [{ required: true, message: '标题不能为空', trigger: 'blur' }],
    content: [{ required: true, message: '内容不能为空', trigger: 'blur' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

// 详情信息
const detailInfo = ref<PolicyVo>({});

/** 查询政策列表 */
const getList = async () => {
  loading.value = true;
  const res = await listPolicy(queryParams.value);
  policyList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  policyFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

// 多选框选中数据
const handleSelectionChange = (selection: PolicyVo[]) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加政策';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: PolicyVo) => {
  reset();
  const id = row?.id || ids.value[0];
  const res = await getPolicy(id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改政策';
};

/** 提交按钮 */
const submitForm = () => {
  policyFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      if (form.value.id) {
        await updatePolicy(form.value);
        proxy?.$modal.msgSuccess('修改成功');
      } else {
        await addPolicy(form.value);
        proxy?.$modal.msgSuccess('新增成功');
      }
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: PolicyVo) => {
  const policyIds = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除政策编号为"' + policyIds + '"的数据项？');
  await delPolicy(policyIds);
  await getList();
  proxy?.$modal.msgSuccess('删除成功');
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.$download.excel(exportPolicy, '政策数据', queryParams.value);
};

/** 查看详情 */
const handleDetail = async (row: PolicyVo) => {
  const res = await getPolicy(row.id);
  detailInfo.value = res.data;
  detailDialog.visible = true;
  detailDialog.title = '政策详情';
};

/** H5预览 */
const handlePreview = () => {
  previewDialog.visible = true;
};

onMounted(() => {
  getList();
});
</script>

<style lang="scss" scoped>
.policy-content {
  max-height: 200px;
  overflow-y: auto;

  :deep(img) {
    max-width: 100%;
    height: auto;
  }
}

.h5-preview-container {
  display: flex;
  justify-content: center;
  padding: 20px 0;
}

.mobile-frame {
  width: 320px;
  background: #fff;
  border: 1px solid #ddd;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.mobile-header {
  background: #f8f9fa;
  padding: 15px;
  border-bottom: 1px solid #eee;
  text-align: center;
}

.mobile-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.mobile-content {
  max-height: 400px;
  overflow-y: auto;
}

.mobile-cover {
  width: 100%;

  img {
    width: 100%;
    height: auto;
    display: block;
  }
}

.mobile-body {
  padding: 15px;
}

.mobile-text {
  font-size: 14px;
  line-height: 1.6;
  color: #333;
  margin-bottom: 15px;

  :deep(img) {
    max-width: 100%;
    height: auto;
  }

  :deep(p) {
    margin: 0 0 10px 0;
  }
}

.mobile-attachment {
  padding-top: 10px;
  border-top: 1px solid #eee;
  text-align: center;
}
</style>