<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="政策标题" prop="title">
              <el-input v-model="queryParams.title" placeholder="请输入政策标题" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="创建人" prop="createBy">
              <el-input v-model="queryParams.createBy" placeholder="请输入创建人" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <!-- todo 根据ts定义，有发布时间吗 -->
            <el-form-item label="发布时间" prop="publishTime">
              <el-date-picker
                v-model="queryParams.publishTimeRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                @change="handleQuery"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['policy:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['policy:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['policy:remove']">删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['policy:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList" />
        </el-row>
      </template>

      <el-table v-loading="loading" border :data="policyList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="序号" type="index" width="60" align="center" />
        <el-table-column label="政策标题" align="center" prop="title" :show-overflow-tooltip="true" />
        <el-table-column label="原始链接" align="center" prop="url" :show-overflow-tooltip="true" />
        <el-table-column label="发布时间" align="center" prop="publishTime" width="180">
          <template #default="scope">
            <span>{{ scope.row.publishTime || '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="创建人" align="center" prop="createBy" width="120">
          <template #default="scope">
            <span>{{ scope.row.createBy || '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="200" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="查看详情" placement="top">
              <el-button link type="primary" icon="View" @click="handleDetail(scope.row)" v-hasPermi="['policy:query']"></el-button>
            </el-tooltip>
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['policy:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['policy:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" :total="total" @pagination="getList" />
    </el-card>

    <!-- 政策表单对话框（新增/编辑/详情） -->
    <el-dialog v-model="dialog.visible" :title="dialog.title" width="800px" append-to-body>
      <el-form ref="policyFormRef" :model="form" :rules="isViewMode ? {} : rules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="标题" prop="title">
              <el-input v-if="!isViewMode" v-model="form.title" placeholder="请输入标题" />
              <span v-else>{{ form.title || '-' }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="原始链接" prop="url">
              <el-input v-if="!isViewMode" v-model="form.url" placeholder="请输入原始链接" />
              <el-link v-else-if="form.url" :href="form.url" target="_blank" type="primary">{{ form.url }}</el-link>
              <span v-else>-</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="发布时间" prop="publishTime">
              <el-date-picker
                v-if="!isViewMode"
                v-model="form.publishTime"
                type="datetime"
                placeholder="选择发布时间"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                style="width: 100%"
              />
              <span v-else>{{ form.publishTime || '-' }}</span>
            </el-form-item>
          </el-col>
          <!-- todo 封面组件能小一点吗 -->
          <el-col :span="12">
            <el-form-item label="封面">
              <image-upload v-if="!isViewMode" v-model="form.cover" :limit="1" />
              <div v-else>
                <image-preview v-if="form.coverUrl" :src="form.coverUrl" :width="100" :height="100" />
                <span v-else>-</span>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="附件">
              <file-upload v-if="!isViewMode" v-model="form.attachment" />
              <div v-else>
                <el-link v-if="form.attachmentUrl" :href="form.attachmentUrl" target="_blank" type="primary">
                  <el-icon><Download /></el-icon>
                  下载附件
                </el-link>
                <span v-else>-</span>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="创建人">
              <span>{{ form.createBy || '-' }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="创建时间">
              <span>{{ proxy?.parseTime(form.createTime) || '-' }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="内容" prop="content">
              <editor v-if="!isViewMode" v-model="form.content" :min-height="300" />
              <div v-else v-html="form.content" class="policy-content"></div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button v-if="isViewMode" @click="handlePreview" type="primary" icon="View">H5预览</el-button>
          <el-button v-if="!isViewMode" @click="cancel">取 消</el-button>
          <el-button v-if="!isViewMode" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="closeDialog">{{ isViewMode ? '关 闭' : '取 消' }}</el-button>
        </div>
      </template>
    </el-dialog>



    <!-- H5预览对话框 -->
    <el-dialog v-model="previewDialog.visible" title="H5预览" width="400px" append-to-body>
      <div class="h5-preview-container">
        <div class="mobile-frame">
          <div class="mobile-header">
            <div class="mobile-title">{{ form.title }}</div>
          </div>
          <div class="mobile-content">
            <div v-if="form.coverUrl" class="mobile-cover">
              <img :src="form.coverUrl" alt="封面" />
            </div>
            <div class="mobile-body">
              <div v-html="form.content" class="mobile-text"></div>
              <div v-if="form.attachmentUrl" class="mobile-attachment">
                <el-link :href="form.attachmentUrl" target="_blank" type="primary">
                  <el-icon><Download /></el-icon>
                  下载附件
                </el-link>
              </div>
            </div>
          </div>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="previewDialog.visible = false">关 闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Policy" lang="ts">
import { listPolicy, getPolicy, delPolicy, addPolicy, updatePolicy, exportPolicy } from '@/api/policy';
import { PolicyVo, PolicyQuery, PolicyForm } from '@/api/policy/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const policyList = ref<PolicyVo[]>([]);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const policyFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const previewDialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

// 是否为查看模式
const isViewMode = ref(false);

const initFormData: PolicyForm = {
  id: undefined,
  title: '',
  content: '',
  url: '',
  cover: undefined,
  attachment: '',
  publishTime: ''
};

const data = reactive<PageData<PolicyForm, PolicyQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    title: '',
    content: '',
    url: '',
    cover: undefined,
    attachment: '',
    createBy: '',
    publishTimeRange: []
  },
  rules: {
    title: [{ required: true, message: '标题不能为空', trigger: 'blur' }],
    content: [{ required: true, message: '内容不能为空', trigger: 'blur' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询政策列表 */
const getList = async () => {
  loading.value = true;
  const res = await listPolicy(queryParams.value);
  policyList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  closeDialog();
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  policyFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

// 多选框选中数据
const handleSelectionChange = (selection: PolicyVo[]) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  isViewMode.value = false;
  dialog.visible = true;
  dialog.title = '添加政策';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: PolicyVo) => {
  reset();
  const id = row?.id || ids.value[0];
  const res = await getPolicy(id);
  Object.assign(form.value, res.data);
  isViewMode.value = false;
  dialog.visible = true;
  dialog.title = '修改政策';
};

/** 提交按钮 */
const submitForm = () => {
  policyFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      if (form.value.id) {
        await updatePolicy(form.value);
        proxy?.$modal.msgSuccess('修改成功');
      } else {
        await addPolicy(form.value);
        proxy?.$modal.msgSuccess('新增成功');
      }
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: PolicyVo) => {
  const policyIds = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除政策编号为"' + policyIds + '"的数据项？');
  await delPolicy(policyIds);
  await getList();
  proxy?.$modal.msgSuccess('删除成功');
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.$download.excel(exportPolicy, '政策数据', queryParams.value);
};

/** 查看详情 */
const handleDetail = async (row: PolicyVo) => {
  const res = await getPolicy(row.id);
  Object.assign(form.value, res.data);
  isViewMode.value = true;
  dialog.visible = true;
  dialog.title = '政策详情';
};

/** 关闭对话框 */
const closeDialog = () => {
  dialog.visible = false;
  isViewMode.value = false;
  reset();
};

/** H5预览 */
const handlePreview = () => {
  previewDialog.visible = true;
};

onMounted(() => {
  getList();
});
</script>

<style lang="scss" scoped>
.policy-content {
  max-height: 200px;
  overflow-y: auto;

  :deep(img) {
    max-width: 100%;
    height: auto;
  }
}

.h5-preview-container {
  display: flex;
  justify-content: center;
  padding: 20px 0;
}

.mobile-frame {
  width: 320px;
  background: #fff;
  border: 1px solid #ddd;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.mobile-header {
  background: #f8f9fa;
  padding: 15px;
  border-bottom: 1px solid #eee;
  text-align: center;
}

.mobile-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.mobile-content {
  max-height: 400px;
  overflow-y: auto;
}

.mobile-cover {
  width: 100%;

  img {
    width: 100%;
    height: auto;
    display: block;
  }
}

.mobile-body {
  padding: 15px;
}

.mobile-text {
  font-size: 14px;
  line-height: 1.6;
  color: #333;
  margin-bottom: 15px;

  :deep(img) {
    max-width: 100%;
    height: auto;
  }

  :deep(p) {
    margin: 0 0 10px 0;
  }
}

.mobile-attachment {
  padding-top: 10px;
  border-top: 1px solid #eee;
  text-align: center;
}
</style>